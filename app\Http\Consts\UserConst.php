<?php

namespace App\Http\Consts;

class UserConst
{
    const userStatusNormal = 'normal';
    const userStatusDisabled = 'disabled';


    const userSexMale = 'male';
    const userSexFemale = 'female';
    const userSexUnknown = 'unknown';

    const callTypeTeenSoldier = 'teen_soldier';
    const callTypeRecallTaskSoldier = 'recall_task_soldier';

    public static function statusMap()
    {
        return [
            self::userStatusNormal   => '正常',
            self::userStatusDisabled => '禁用',
        ];
    }

    public static function sexMap()
    {
        return [
            self::userSexMale    => '男',
            self::userSexFemale  => '女',
            self::userSexUnknown => '未知',
        ];
    }
}
