<?php

namespace App\Http\Controllers\Common;

use App\Http\Services\Common\NotificationService;

class NotificationController
{
    public function notification()
    {
        $params  = request()->all();
        $service = new NotificationService();
        $resp    = $service->notification($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function notificationDetail($id)
    {
        $service = new NotificationService();
        $resp    = $service->notificationDetail($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }


    public function notificationRead($id)
    {
        $service = new NotificationService();
        $resp    = $service->notificationRead($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
