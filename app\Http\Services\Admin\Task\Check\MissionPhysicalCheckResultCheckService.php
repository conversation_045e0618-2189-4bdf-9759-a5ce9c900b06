<?php

namespace App\Http\Services\Admin\Task\Check;

use App\Exceptions\ErrorTrait;
use App\Http\Consts\ImportConst;
use App\Http\Consts\MissionPersonConst;
use App\Models\ImportItem;
use App\Models\MissionPerson;
use Illuminate\Support\Str;

class MissionPhysicalCheckResultCheckService
{
    use ErrorTrait;

    private $task;

    private $missionId;

    public function handle($task)
    {
        $this->task      = $task;
        $this->missionId = $task->params['mission_id'] ?? null;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE');

        $queryBuilder = ImportItem::query()->where('task_id', $task->id)->whereIn('checked', [
            ImportConst::importItemCheckUncheck,
            ImportConst::importItemCheckError,
        ]);

        $queryBuilder->clone()->where('type', ImportConst::importItemTypeMissionPhysicalCheckResult)->chunkById($INSERT_BATCH_SIZE, function ($items) use ($task) {
            $this->checkPhysicalCheckResult($items);
        });
    }

    public function checkPhysicalCheckResult($items)
    {
        foreach ($items as $item) {
            $message = '';
            $data    = $item['data'];
            $name    = $data['name'];
            $idCard  = $data['id_card'];
            $result  = $data['result'];
            $remark  = $data['remark'] ?? '';

            if (!$name) {
                $message = "姓名不能为空";
            }
            if (!$idCard) {
                $message = "身份证不能为空";
            }
            if (Str::length($idCard) != 18) {
                $message = "身份证长度应为18位";
            }
            if (!$result) {
                $message = "体检结果不能为空";
            } else {
                // 验证结果值是否有效
                $validResults = ['合格', '不合格', '1', '2'];
                if (!in_array($result, $validResults)) {
                    $message = "体检结果值无效，应为：合格、不合格、1或2";
                } else {
                    // 如果结果是不合格，检查是否填写了不合格原因
                    if (in_array($result, ['不合格', '2']) && empty($remark)) {
                        $message = "体检结果为不合格时，不合格原因不能为空";
                    }
                }
            }

            // 检查人员是否存在
            if (!$message && $this->missionId) {
                $person = MissionPerson::query()
                    ->where('mission_id', $this->missionId)
                    ->where('id_card', $idCard)
                    ->first();
                
                if (!$person) {
                    $message = "未找到对应的人员记录";
                }
            }

            if ($message) {
                $item->update([
                    "message" => $message,
                    "checked" => ImportConst::importItemCheckError,
                ]);
            } else {
                $item->update([
                    "checked" => ImportConst::importItemCheckCheckAccepted,
                ]);
            }
        }
    }
}
