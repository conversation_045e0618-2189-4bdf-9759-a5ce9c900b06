<?php

namespace App\Http\Controllers\Mini;

use App\Http\Services\Mini\RegionService;

class RegionController
{
    public function departments()
    {
        $params  = request()->input();
        $service = new RegionService();
        $resp    = $service->departments($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function tree()
    {
        $params  = request()->input();
        $service = new RegionService();
        $resp    = $service->tree($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
