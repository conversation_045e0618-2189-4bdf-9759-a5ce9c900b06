<?php

namespace App\Http\Services\Admin\Task\Import;

use App\Exceptions\ErrorTrait;
use App\Http\Consts\ImportConst;
use App\Http\Consts\MissionPersonConst;
use App\Models\ImportItem;
use App\Models\MissionPerson;

class MissionPhysicalSpotCheckResultImportService
{
    use ErrorTrait;

    private $task;

    private $missionId;

    public function handle($task)
    {
        $this->task      = $task;
        $this->missionId = $task->params['mission_id'] ?? null;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE');

        $queryBuilder = ImportItem::query()->where('task_id', $task->id)->whereIn('checked', [
            ImportConst::importItemCheckCheckAccepted,
        ])->whereIn('imported', [
            ImportConst::importItemImportedToDo,
            ImportConst::importItemCheckError,
        ]);

        $queryBuilder->clone()->where('type', ImportConst::importItemTypeMissionPhysicalSpotCheckResult)->chunkById($INSERT_BATCH_SIZE, function ($items) use ($task) {
            $this->importPhysicalSpotCheckResult($items);
        });
    }

    private function importPhysicalSpotCheckResult($items)
    {
        foreach ($items as $item) {
            $data   = $item['data'];
            $idCard = $data['id_card'];
            $result = $data['result'];

            // 转换结果值
            $physicalSpotCheck = $this->convertResultToPhysicalSpotCheck($result);
            $physicalResult = $this->convertResultToPhysicalResult($result);

            // 更新人员记录
            MissionPerson::query()
                ->where('mission_id', $this->missionId)
                ->where('id_card', $idCard)
                ->update([
                    'physical_spot_check' => $physicalSpotCheck,
                    'physical_result' => $physicalResult,
                ]);

            $item->update([
                "imported" => ImportConst::importItemImportedDone,
            ]);
        }
    }

    private function convertResultToPhysicalSpotCheck($result)
    {
        switch ($result) {
            case '正常':
            case '1':
                return MissionPersonConst::personPhysicalSpotCheckYes;
            case '异常':
            case '2':
                return MissionPersonConst::personPhysicalSpotCheckNo;
            default:
                return MissionPersonConst::personPhysicalSpotCheckUnknown;
        }
    }

    private function convertResultToPhysicalResult($result)
    {
        switch ($result) {
            case '正常':
            case '1':
                return MissionPersonConst::personPhysicalResultYes;
            case '异常':
            case '2':
                return MissionPersonConst::personPhysicalResultNo;
            default:
                return MissionPersonConst::personPhysicalResultUnknown;
        }
    }
}
