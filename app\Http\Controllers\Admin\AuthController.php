<?php

namespace App\Http\Controllers\Admin;

use App\Http\Services\Admin\AuthService;

class AuthController
{
    public function login()
    {
        $service = new AuthService();
        $resp    = $service->login(request()->all());
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    public function info()
    {
        $service = new AuthService();
        $resp    = $service->info();
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }


    public function navs()
    {
        $service = new AuthService();
        $resp    = $service->navs();
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function updatePassword()
    {
        $params  = request()->all();
        $service = new AuthService();
        $resp    = $service->updatePassword($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
