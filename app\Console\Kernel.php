<?php

namespace App\Console;

use App\Http\Consts\OutNoticeConst;
use App\Http\Consts\SurveyConst;
use App\Http\Services\Common\FileService;
use App\Models\OperationLog;
use App\Models\OutNoticeTaskReceive;
use App\Models\Survey;
use App\Models\User;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Log;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $fileService = new FileService();
        // 上传百炼资料
        $schedule->command('app:bailian-database')->everyMinute();

        // 账号过期
        $schedule->call(function () {
            $next_account_expire_date = get_config('next_account_expire_date');
            if (now()->format('Y-m-d') == $next_account_expire_date) {
                User::query()->update([
                    'activated'    => 0,
                    'activated_at' => null,
                ]);
            }
        })->at('01:00');

        // 获取通话录音上传到自己oss
        $schedule->call(function () use ($fileService) {
            $outNoticeTaskReceive = OutNoticeTaskReceive::query()->where('status', OutNoticeConst::taskReceiveStatusFinished)
                ->whereNotNull("record_file")
                ->whereNull("record_file_oss")
                ->first();
            if ($outNoticeTaskReceive) {
                $recordFile = $outNoticeTaskReceive->record_file;
                Log::info("开始上传通话录音文件：" . $recordFile);
                $resp = $fileService->fetchAndUploadFile($recordFile, 'wav');
                if ($fileService->hasError()) {
                    $outNoticeTaskReceive->record_file_oss = false;
                    $outNoticeTaskReceive->save();
                } else {
                    $outNoticeTaskReceive->record_file_oss = $resp['id'];
                    $outNoticeTaskReceive->save();
                }
            }
        })->everyMinute();

        // 日志保存半年
        $schedule->call(function () {
            OperationLog::query()->where('created_at', '<', now()->subMonths(6))->delete();
        })->daily();


        // 调查表
        $schedule->call(function () {
            Survey::query()->where('status', SurveyConst::statusPending)->where('start_at', '<=', now())->update([
                'status' => SurveyConst::statusRunning,
            ]);
            Survey::query()->where('status', SurveyConst::statusRunning)->where('end_at', '<=', now())->update([
                'status' => SurveyConst::statusFinished,
            ]);
        })->everyMinute();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
