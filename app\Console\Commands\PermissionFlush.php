<?php

namespace App\Console\Commands;

use App\Http\Utils\PermissionUtil;
use App\Models\Permission;
use Illuminate\Console\Command;

class PermissionFlush extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:permission-flush';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        PermissionUtil::generatePermissionToMysql();
        $permissions = Permission::query()->get();
        $slugs       = [];
        $permissions->each(function (Permission $permission) use (&$slugs) {
            $slugs[$permission->slug] = [
                'text'   => $permission->name,
                'status' => $permission->slug,
            ];
        });
        file_put_contents(resource_path('json/permission.json'), json_encode($slugs, JSON_UNESCAPED_UNICODE));
    }
}
