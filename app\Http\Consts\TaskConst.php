<?php

namespace App\Http\Consts;

class TaskConst
{
    const taskTypeImportUserExcel = 'import_user_excel'; // 导入用户Excel
    const taskTypeExportUserExcel = 'export_user_excel'; // 导出用户Excel
    const taskTypeGenerateInvitationCode = 'generate_invitation_code';
    const taskTypeExportInvitationCodeExcel = 'export_invitation_code_excel';
    const taskTypeImportOutNoticeTaskReceiveExcel = 'import_out_notice_task_receive_excel';
    const taskTypeExportOutNoticeTaskReceiveExcel = 'export_out_notice_task_receive_excel';

    const taskTypeImportTeenSoldierExcel = 'import_teen_soldier_excel';
    const taskTypeExportTeenSoldierExcel = 'export_teen_soldier_excel';

    const taskTypeImportEnlistSoldierExcel = 'import_enlist_soldier_excel';
    const taskTypeExportEnlistSoldierExcel = 'export_enlist_soldier_excel';

    const taskTypeExportExSoldierExcel = 'export_ex_soldier_excel';
    const taskTypeImportExSoldierExcel = 'import_ex_soldier_excel';

    const taskTypeImportRecallTaskSoldierExcel = 'import_recall_task_soldier_excel';
    const taskTypeExportRecallTaskSoldierExcel = 'export_recall_task_soldier_excel';
    const taskTypeExportRecallTaskSoldierLinkExcel = 'export_recall_task_soldier_link_excel';

    const taskTypeImportQuestionExcel = 'import_question_excel';
    const taskTypeExportQuestionExcel = 'export_question_excel';

    const taskTypeImportKnowledgeExcel = 'import_knowledge_excel';
    const taskTypeExportKnowledgeExcel = 'export_knowledge_excel';


    const taskTypeImportConsultKnowledgeExcel = 'import_consult_knowledge_excel';
    const taskTypeExportConsultKnowledgeExcel = 'export_consult_knowledge_excel';

    // 统计相关导出
    const taskTypeExportStatisticsUserCourse = 'export_statistics_user_course';
    const taskTypeExportStatisticsDepartmentCourse = 'export_statistics_department_course';
    const taskTypeExportStatisticsCourse = 'export_statistics_course';
    const taskTypeExportStatisticsUserPaper = 'export_statistics_user_paper';
    const taskTypeExportStatisticsDepartmentPaper = 'export_statistics_department_paper';
    const taskTypeExportStatisticsUserPoint = 'export_statistics_user_point';
    const taskTypeExportStatisticsUserCall = 'export_statistics_user_call';
    const taskTypeRefreshStatisticsCourse = 'refresh_statistics_course';

    const taskTypeExportWorkTaskReport = 'export_work_task_report';

    const taskTypeExportSurveyFormExcel = 'export_survey_form_excel';

    const taskTypeImportMissionInit = 'import_mission_init';

    const taskTypeExportMissionPerson = 'export_mission_person';

    const taskTypeMissionPromoteCompare = 'mission_promote_compare';

    const taskTypeImportMissionPromoteComparePersonSignExcel = 'import_mission_promote_compare_person_sign_excel';

    const taskTypeMissionPhysicalExaminationAddPerson = 'mission_physical_examination_add_person';

    const taskTypeMissionPhysicalExaminationRecheckAddPerson = 'mission_physical_examination_recheck_add_person';

    // mission_physical_examination_gen_spot_check_list
    const taskTypeMissionPhysicalExaminationGenSpotCheckList = 'mission_physical_examination_gen_spot_check_list';

    const taskTypeMissionToIntranet = 'export_mission_to_intranet';

    const taskTypeMissionImportMissionIntranetPoliticalPersonList = 'import_mission_intranet_political_person_list';
    const taskTypeMissionImportMissionIntranetEducationPersonList = 'import_mission_intranet_education_person_list';
    const taskTypeMissionImportMissionIntranetPreStorePersonList = 'import_mission_intranet_pre_store_person_list';
    const taskTypeMissionImportMissionIntranetGoPersonStatisticsList = 'import_mission_intranet_go_person_statistics_list';
    const taskTypeMissionClearMissionSensitiveInfo = 'clear_mission_sensitive_info';


    const taskTypeRefreshMissionPromoteCompareStatisticsList = "refresh_mission_promote_compare_statistics_list";
    const taskTypeExportMissionPromoteCompareStatisticsList = "export_mission_promote_compare_statistics_list";
    const taskTypeRefreshMissionIntentionStatisticsList = 'refresh_mission_intention_statistics_list';
    const taskTypeExportMissionIntentionStatisticsList = "export_mission_intention_statistics_list";
    const taskTypeRefreshMissionPoliticalExamStatisticsList = "refresh_mission_political_exam_statistics_list";
    const taskTypeExportMissionPoliticalExamStatisticsList = "export_mission_political_exam_statistics_list";
    const taskTypeRefreshMissionEducationStatisticsList = "refresh_mission_education_statistics_list";
    const taskTypeExportMissionEducationStatisticsList = "export_mission_education_statistics_list";
    const taskTypeRefreshMissionPreStoreStatisticsList = "refresh_mission_pre_store_statistics_list";
    const taskTypeExportMissionPreStoreStatisticsList = "export_mission_pre_store_statistics_list";
    const taskTypeRefreshMissionTaskNumStatisticsList = "refresh_mission_task_num_statistics_list";
    const taskTypeExportMissionTaskNumStatisticsList = "export_mission_task_num_statistics_list";
    const taskTypeRefreshMissionDetailStatistics = "refresh_mission_detail_statistics";

    const taskTypeRefreshMissionPhysicalTaskNumStatisticsList = "refresh_mission_physical_task_num_statistics_list";
    const taskTypeExportMissionPhysicalTaskNumStatisticsList = "export_mission_physical_task_num_statistics_list";
    const taskTypeRefreshMissionPhysicalCheckStatisticsList = "refresh_mission_physical_check_statistics_list";
    const taskTypeExportMissionPhysicalCheckStatisticsList = "export_mission_physical_check_statistics_list";
    const taskTypeRefreshMissionPhysicalRecheckStatisticsList = "refresh_mission_physical_recheck_statistics_list";
    const taskTypeExportMissionPhysicalRecheckStatisticsList = "export_mission_physical_recheck_statistics_list";
    const taskTypeRefreshMissionPhysicalSpotCheckStatisticsList = "refresh_mission_physical_spot_check_statistics_list";
    const taskTypeExportMissionPhysicalSpotCheckStatisticsList = "export_mission_physical_spot_check_statistics_list";
    const taskTypeRefreshMissionSummaryStatisticsList = "refresh_mission_summary_statistics_list";
    const taskTypeExportMissionSummaryStatisticsList = "export_mission_summary_statistics_list";
    const taskTypeExportMissionGoPersonStatisticsList = "export_mission_go_person_statistics_list";
    const taskTypeExportMissionRegionSettings = "export_mission_region_settings";

    const taskTypeExportMissionEducationPersonList = 'export_mission_education_person_list';
    const taskTypeExportMissionPreStorePersonList = 'export_mission_pre_store_person_list';
    const taskTypeExportMissionIntentionPersonList = 'export_mission_intention_person_list';
    const taskTypeExportMissionPhysicalCheckPersonList = 'export_mission_physical_check_person_list';
    const taskTypeExportMissionPhysicalCheckReportPersonList = 'export_mission_physical_check_report_person_list';
    const taskTypeExportMissionPhysicalRecheckPersonList = 'export_mission_physical_recheck_person_list';
    const taskTypeExportMissionPhysicalSpotCheckPersonList = 'export_mission_physical_spot_check_person_list';
    const taskTypeExportMissionPromoteComparePersonList = 'export_mission_promote_compare_person_list';
    const taskTypeExportMissionPromoteCompareIntentionPersonList = 'export_mission_promote_compare_intention_person_list';
    const taskTypeExportMissionPromoteComparePersonSignList = 'export_mission_promote_compare_person_sign_list';
    const taskTypeExportMissionPoliticalPersonList = 'export_mission_political_person_list';
    const taskTypeExportMissionPhysicalPlanRecordList = 'export_mission_physical_plan_record_list';

    const taskTypeRefreshMissionSchoolPersonStatisticsChart = "refresh_mission_school_person_statistics_chart";
    const taskTypeRefreshMissionSchoolPersonStatisticsList = "refresh_mission_school_person_statistics_list";
    const taskTypeExportMissionSchoolPersonStatisticsList = "export_mission_school_person_statistics_list";
    const taskTypeRefreshMissionSocialPersonStatisticsList = "refresh_mission_social_person_statistics_list";
    const taskTypeRefreshMissionSocialPersonStatisticsChart = "refresh_mission_social_person_statistics_chart";
    const taskTypeExportMissionSocialPersonStatisticsList = "export_mission_social_person_statistics_list";

    const taskTypeImportMissionPhysicalCheckResult = 'import_mission_physical_check_result';
    const taskTypeImportMissionPhysicalRecheckResult = 'import_mission_physical_recheck_result';
    const taskTypeImportMissionPhysicalSpotCheckResult = 'import_mission_physical_spot_check_result';


    const taskTypeMap
        = [
            self::taskTypeImportUserExcel => '导入用户Excel',
            self::taskTypeExportUserExcel => '导出用户Excel',

            self::taskTypeGenerateInvitationCode    => '生成注册码',
            self::taskTypeExportInvitationCodeExcel => '导出注册码Excel',

            self::taskTypeImportOutNoticeTaskReceiveExcel => '导入外放任务接收Excel',
            self::taskTypeExportOutNoticeTaskReceiveExcel => '导出外放任务接收Excel',

            self::taskTypeImportTeenSoldierExcel => '导入应征青年Excel',
            self::taskTypeExportTeenSoldierExcel => '导出应征青年Excel',

            self::taskTypeImportEnlistSoldierExcel => '导入在伍士兵Excel',
            self::taskTypeExportEnlistSoldierExcel => '导出在伍士兵Excel',

            self::taskTypeImportExSoldierExcel => '导入退伍士兵Excel',
            self::taskTypeExportExSoldierExcel => '导出退伍士兵Excel',

            self::taskTypeImportRecallTaskSoldierExcel     => '导入召回任务士兵Excel',
            self::taskTypeExportRecallTaskSoldierExcel     => '导出召回任务士兵Excel',
            self::taskTypeExportRecallTaskSoldierLinkExcel => '导出召回任务士兵链接Excel',

            self::taskTypeImportQuestionExcel => '导入问题Excel',
            self::taskTypeExportQuestionExcel => '导出问题Excel',

            self::taskTypeImportKnowledgeExcel => '导入知识点Excel',
            self::taskTypeExportKnowledgeExcel => '导出知识点Excel',

            self::taskTypeImportConsultKnowledgeExcel => '导入咨询知识点Excel',
            self::taskTypeExportConsultKnowledgeExcel => '导出咨询知识点Excel',

            self::taskTypeExportStatisticsUserCourse       => '导出用户课程统计',
            self::taskTypeExportStatisticsDepartmentCourse => '导出部门课程统计',
            self::taskTypeExportStatisticsCourse           => '导出课程统计',

            self::taskTypeExportStatisticsUserPaper       => '导出用户试卷统计',
            self::taskTypeExportStatisticsDepartmentPaper => '导出部门试卷统计',

            self::taskTypeExportStatisticsUserPoint => '导出用户积分统计',
            self::taskTypeExportStatisticsUserCall  => '导出用户通话统计',

            self::taskTypeRefreshStatisticsCourse => '刷新课程统计',
            self::taskTypeExportWorkTaskReport    => '导出工作任务报告',

            self::taskTypeExportSurveyFormExcel                     => '导出问卷表Excel',

            // 人员统计
            self::taskTypeRefreshMissionSocialPersonStatisticsChart => '刷新任务社会人员统计图表',
            self::taskTypeRefreshMissionSocialPersonStatisticsList  => '刷新任务社会人员统计列表',
            self::taskTypeExportMissionSocialPersonStatisticsList  => '导出社会适龄青年统计数据',
            self::taskTypeRefreshMissionSchoolPersonStatisticsChart => '刷新任务高校人员统计图表',
            self::taskTypeRefreshMissionSchoolPersonStatisticsList  => '刷新任务高校人员统计列表',
            self::taskTypeExportMissionSchoolPersonStatisticsList  => '导出高校人员统计数据',

            self::taskTypeImportMissionInit                                  => '导入任务初始化数据',
            self::taskTypeMissionToIntranet                                  => '导出任务到内网',
            self::taskTypeMissionImportMissionIntranetPoliticalPersonList    => '导入内网政考人员名单',
            self::taskTypeMissionImportMissionIntranetEducationPersonList    => '导入内网役前教育人员名单',
            self::taskTypeMissionImportMissionIntranetPreStorePersonList     => '导入内网预储人员名单',
            self::taskTypeMissionImportMissionIntranetGoPersonStatisticsList => '导入内网起运人员数据',

            self::taskTypeMissionClearMissionSensitiveInfo => '清理敏感数据',

            self::taskTypeImportMissionPromoteComparePersonSignExcel => '导入任务报名人员',
            self::taskTypeExportMissionPerson                        => '导出任务人员',

            self::taskTypeMissionPromoteCompare               => '报名意向比对',
            self::taskTypeMissionPhysicalExaminationAddPerson => '体检计划添加人员',

            self::taskTypeMissionPhysicalExaminationRecheckAddPerson => '体检复查添加人员',

            self::taskTypeMissionPhysicalExaminationGenSpotCheckList => '生成体检抽查名单',


            // 数据刷新
            self::taskTypeRefreshMissionPromoteCompareStatisticsList => '刷新宣传对比数据',
            self::taskTypeExportMissionPromoteCompareStatisticsList => '导出宣传对比数据',
            self::taskTypeRefreshMissionIntentionStatisticsList      => '刷新意向数据',
            self::taskTypeExportMissionIntentionStatisticsList      => '导出意向数据',

            self::taskTypeRefreshMissionPhysicalTaskNumStatisticsList   => '体检任务数据重新统计',
            self::taskTypeExportMissionPhysicalTaskNumStatisticsList   => '导出体检任务数据',
            self::taskTypeRefreshMissionPhysicalCheckStatisticsList     => '体检数据重新统计',
            self::taskTypeExportMissionPhysicalCheckStatisticsList     => '导出体检数据',
            self::taskTypeRefreshMissionPhysicalRecheckStatisticsList   => '复查数据重新统计',
            self::taskTypeExportMissionPhysicalRecheckStatisticsList   => '导出复查数据',
            self::taskTypeRefreshMissionPhysicalSpotCheckStatisticsList => '抽查数据重新统计',
            self::taskTypeExportMissionPhysicalSpotCheckStatisticsList => '导出抽查数据',
            self::taskTypeRefreshMissionPoliticalExamStatisticsList     => '刷新政考数据',
            self::taskTypeExportMissionPoliticalExamStatisticsList     => '导出政考数据',
            self::taskTypeRefreshMissionEducationStatisticsList         => '刷新役前教育数据',
            self::taskTypeExportMissionEducationStatisticsList         => '导出役前教育数据',
            self::taskTypeRefreshMissionPreStoreStatisticsList          => '刷新预储数据',
            self::taskTypeExportMissionPreStoreStatisticsList          => '导出预储数据',
            self::taskTypeRefreshMissionSummaryStatisticsList           => '刷新任务汇总数据',
            self::taskTypeExportMissionSummaryStatisticsList           => '导出任务汇总数据',
            self::taskTypeExportMissionGoPersonStatisticsList          => '导出起运人员统计数据',
            self::taskTypeRefreshMissionTaskNumStatisticsList           => '刷新任务数据',
            self::taskTypeExportMissionTaskNumStatisticsList            => '导出任务数据',
            self::taskTypeRefreshMissionDetailStatistics                => '刷新任务详情数据',

            self::taskTypeExportMissionEducationPersonList             => '导出役前教育人员名单',
            self::taskTypeExportMissionPreStorePersonList              => '导出预储人员名单',
            self::taskTypeExportMissionIntentionPersonList             => '导出意向人员名单',
            self::taskTypeExportMissionPhysicalCheckPersonList         => '导出体检人员名单',
            self::taskTypeExportMissionPhysicalCheckReportPersonList   => '导出体检结果人员名单',
            self::taskTypeExportMissionPhysicalRecheckPersonList       => '导出复查人员名单',
            self::taskTypeExportMissionPhysicalSpotCheckPersonList     => '导出抽查人员名单',
            self::taskTypeExportMissionPromoteComparePersonList        => '导出宣传比对人员名单',
            self::taskTypeExportMissionPromoteCompareIntentionPersonList => '导出宣传比对意向人员名单',
            self::taskTypeExportMissionPromoteComparePersonSignList    => '导出宣传比对报名人员名单',
            self::taskTypeExportMissionPoliticalPersonList             => '导出政考人员名单',
            self::taskTypeExportMissionPhysicalPlanRecordList          => '导出体检计划记录人员名单',
            self::taskTypeExportMissionRegionSettings                  => '导出任务任务数设置',

            self::taskTypeImportMissionPhysicalCheckResult => '导入初次体检结果',
            self::taskTypeImportMissionPhysicalRecheckResult => '导入体检复查结果',
            self::taskTypeImportMissionPhysicalSpotCheckResult => '导入体检抽查结果',
        ];


    const taskStatusPending = 'pending';
    const taskStatusRunning = 'running';
    const taskStatusSuccess = 'success';
    const taskStatusFailed = 'failed';


    const taskImportStatusPending = 'pending';
    const taskImportStatusParsing = 'parsing';
    const taskImportStatusParsed = 'parsed';
    const taskImportStatusChecking = 'checking';
    const taskImportStatusChecked = 'checked';
    const taskImportStatusImporting = 'importing';
    const taskImportStatusImported = 'imported';
    const taskImportStatusFailed = 'failed';
}
