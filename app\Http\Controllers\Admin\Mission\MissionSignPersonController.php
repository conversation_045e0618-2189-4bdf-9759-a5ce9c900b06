<?php

namespace App\Http\Controllers\Admin\Mission;

use App\Http\Services\Admin\Mission\MissionSignService;
use Illuminate\Http\Request;

class MissionSignPersonController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new MissionSignService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new MissionSignService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new MissionSignService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new MissionSignService();
        $resp    = $service->update($params, $id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $service  = new MissionSignService();
        $params   = $request->all();
        $response = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function append(Request $request)
    {
        $service  = new MissionSignService();
        $params   = $request->all();
        $response = $service->append($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }
}
