<?php

namespace App\Http\Controllers\Admin\Work;

use App\Http\Services\Admin\Work\SurveyFormService;
use Illuminate\Http\Request;

class SurveyFormController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new SurveyFormService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }


    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new SurveyFormService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }


    public function destroy(Request $request, $id)
    {
        $params  = $request->all();
        $service = new SurveyFormService();
        $resp    = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
