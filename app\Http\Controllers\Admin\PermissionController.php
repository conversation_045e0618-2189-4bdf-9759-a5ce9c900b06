<?php

namespace App\Http\Controllers\Admin;

use App\Http\Services\Admin\PermissionService;
use Illuminate\Http\Request;

class PermissionController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new PermissionService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new PermissionService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new PermissionService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new PermissionService();
        $resp    = $service->update($params, $id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $params  = $request->all();
        $service = new PermissionService();
        $resp    = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }


    public function getPermissionTree()
    {
        $service = new PermissionService();
        $resp    = $service->getPermissionTree();
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
