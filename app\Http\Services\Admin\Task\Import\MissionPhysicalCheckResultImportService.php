<?php

namespace App\Http\Services\Admin\Task\Import;

use App\Exceptions\ErrorTrait;
use App\Http\Consts\ImportConst;
use App\Http\Consts\MissionPersonConst;
use App\Models\ImportItem;
use App\Models\MissionPerson;

class MissionPhysicalCheckResultImportService
{
    use ErrorTrait;

    private $task;

    private $missionId;

    public function handle($task)
    {
        $this->task      = $task;
        $this->missionId = $task->params['mission_id'] ?? null;

        $INSERT_BATCH_SIZE = env('INSERT_BATCH_SIZE');

        $queryBuilder = ImportItem::query()->where('task_id', $task->id)->whereIn('checked', [
            ImportConst::importItemCheckCheckAccepted,
        ])->whereIn('imported', [
            ImportConst::importItemImportedToDo,
            ImportConst::importItemCheckError,
        ]);

        $queryBuilder->clone()->where('type', ImportConst::importItemTypeMissionPhysicalCheckResult)->chunkById($INSERT_BATCH_SIZE, function ($items) use ($task) {
            $this->importPhysicalCheckResult($items);
        });
    }

    private function importPhysicalCheckResult($items)
    {
        foreach ($items as $item) {
            // 确保数据正确解析
            if (is_string($item['data'])) {
                $data = json_decode($item['data'], true);
                \Log::info("从JSON字符串解析数据", ['raw_data' => $item['data'], 'parsed' => $data]);
            } else {
                $data = $item['data'];
                \Log::info("直接使用数据", ['data_type' => gettype($data)]);
            }

            // 记录原始数据
            \Log::info("原始数据", ['data' => $data]);

            $idCard = trim($data['id_card'] ?? ''); // 去除空格
            $result = trim($data['result'] ?? '');
            $remark = trim($data['remark'] ?? '');

            // 转换结果值
            $physicalCheck = $this->convertResultToPhysicalCheck($result);
            $physicalResult = $this->convertResultToPhysicalResult($result);

            // 调试信息
            \Log::info("导入体检结果", [
                'mission_id' => $this->missionId,
                'id_card' => $idCard,
                'result' => $result,
                'physical_check' => $physicalCheck,
                'physical_result' => $physicalResult,
                'remark' => $remark
            ]);

            // 查找人员记录 - 先检查是否有多条记录
            $persons = MissionPerson::query()
                ->where('mission_id', $this->missionId)
                ->where('id_card', $idCard)
                ->get();

            \Log::info("查找人员记录", [
                'mission_id' => $this->missionId,
                'id_card' => $idCard,
                'found_count' => $persons->count()
            ]);

            if ($persons->count() > 1) {
                \Log::warning("发现多条人员记录", [
                    'mission_id' => $this->missionId,
                    'id_card' => $idCard,
                    'count' => $persons->count()
                ]);
            }

            if ($persons->count() > 0) {
                $person = $persons->first();
                \Log::info("找到人员记录", [
                    'person_id' => $person->id,
                    'name' => $person->name,
                    'current_physical_check' => $person->physical_check
                ]);

                // 更新人员记录
                $updateResult = $person->update([
                    'physical_check' => $physicalCheck,
                    'physical_result' => $physicalResult,
                    'physical_check_remark' => $remark,
                    'physical_check_at' => now(),
                ]);

                // 重新查询验证更新结果
                $person->refresh();
                \Log::info("更新后的记录", [
                    'update_result' => $updateResult,
                    'new_physical_check' => $person->physical_check,
                    'new_physical_result' => $person->physical_result,
                    'new_remark' => $person->physical_check_remark
                ]);
            } else {
                \Log::warning("未找到人员记录", [
                    'mission_id' => $this->missionId,
                    'id_card' => $idCard
                ]);

                // 尝试模糊查找
                $similarPersons = MissionPerson::query()
                    ->where('mission_id', $this->missionId)
                    ->where('id_card', 'like', '%' . substr($idCard, -6) . '%')
                    ->get(['id', 'name', 'id_card']);

                if ($similarPersons->count() > 0) {
                    \Log::info("找到相似身份证号的记录", [
                        'similar_persons' => $similarPersons->toArray()
                    ]);
                }
            }

            $item->update([
                "imported" => ImportConst::importItemImportedDone,
            ]);
        }
    }

    private function convertResultToPhysicalCheck($result)
    {
        switch ($result) {
            case '合格':
            case '1':
                return MissionPersonConst::personPhysicalCheckYes;
            case '不合格':
            case '2':
                return MissionPersonConst::personPhysicalCheckNo;
            default:
                return MissionPersonConst::personPhysicalCheckUnknown;
        }
    }

    private function convertResultToPhysicalResult($result)
    {

        switch ($result) {
            case '合格':
            case '1':
                return MissionPersonConst::personPhysicalResultYes;
            case '不合格':
            case '2':
                return MissionPersonConst::personPhysicalResultNo;
            default:
                return MissionPersonConst::personPhysicalResultUnknown;
        }
    }
}
