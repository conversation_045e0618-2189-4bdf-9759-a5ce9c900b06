<?php

namespace App\Http\Controllers\Mini;

use App\Http\Services\Mini\ReportService;

class ReportController
{
    public function list()
    {
        $params = request()->all();

        $service = new ReportService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store()
    {
        $params = request()->all();

        $service = new ReportService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show($id)
    {
        $params = request()->all();

        $service = new ReportService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function reply($id)
    {
        $params = request()->all();

        $service = new ReportService();
        $resp    = $service->reply($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function statistics()
    {

        $service = new ReportService();
        $resp    = $service->statistics();
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
