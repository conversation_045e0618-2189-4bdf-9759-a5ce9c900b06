<?php

namespace App\Http\Utils;

use App\Http\Consts\ArticleConst;
use App\Http\Consts\MissionConst;
use App\Http\Consts\MissionPersonConst;
use App\Http\Consts\PaperConst;
use App\Http\Consts\PermissionSlugConst;
use App\Http\Consts\RegionConst;
use App\Http\Consts\RoleConst;
use App\Http\Consts\SurveyConst;
use App\Http\Consts\SystemNotificationConst;
use App\Http\Consts\UserConst;
use App\Http\Consts\WorkTaskConst;
use App\Models\Article;
use App\Models\Inform;
use App\Models\InvitationCode;
use App\Models\MissionPerson;
use App\Models\MissionPhysicalExamination;
use App\Models\MissionRegionSetting;
use App\Models\MissionSignPerson;
use App\Models\OutNoticeTask;
use App\Models\PaperRound;
use App\Models\RecallTask;
use App\Models\RecallTaskSoldier;
use App\Models\Region;
use App\Models\Report;
use App\Models\StatisticsCourse;
use App\Models\StatisticsUserCourse;
use App\Models\Survey;
use App\Models\SurveyForm;
use App\Models\SystemNotification;
use App\Models\User;
use App\Models\WorkTask;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class ScopeQueryImpl implements ScopeQuery
{
    public static function miniGetReportQuery($user, $params)
    {
        $builder = Report::query();
        if (isset($params['scope']) && $params['scope'] != '') {
            $scope = $params['scope'];
            if ($scope == 'create') {
                $builder->where('user_id', $user->id);
            } elseif ($scope == 'can_handle') {
                if (!$user->hasPermission([PermissionSlugConst::mini_report_reply, PermissionSlugConst::admin_report_reply])) {
                    throw new \Exception('没有权限');
                }
                $regionTreeCodes = $user->getManagerRegionTreeCode();
                if (count($regionTreeCodes) == 0) {
                    throw new \Exception('没有权限');
                }
                $builder->where(function ($query) use ($regionTreeCodes) {
                    foreach ($regionTreeCodes as $regionTreeCode) {
                        $query->orWhere('department_tree_code', 'like', $regionTreeCode . '%');
                    }
                });
            } else if ($scope == 'can_see') {
                $regionTreeCodes = $user->getManagerRegionTreeCode();
//                if (count($regionTreeCodes) == 0) {
//                    throw new \Exception('没有权限');
//                }
                $builder->where(function ($query) use ($regionTreeCodes, $user) {
                    $query->where(function ($query) use ($regionTreeCodes) {
                        foreach ($regionTreeCodes as $regionTreeCode) {
                            $query->orWhere('department_tree_code', 'like', $regionTreeCode . '%');
                        }
                    })->orWhere('user_id', $user->id);
                });
            } else {
                throw new \Exception('没有权限');
            }
        } else {
            throw new \Exception('没有权限');
        }

        if (isset($params['keyword']) && $params['keyword'] != '') {
            $keyword = $params['keyword'];
            $builder->where(function ($query) use ($keyword) {
                $query->where('content', 'like', '%' . $keyword . '%')
                    ->orWhere('phone', 'like', '%' . $keyword . '%');
            });
        }

        // department_tree_code
        if (isset($params['department_tree_code']) && $params['department_tree_code'] != '') {
            $departmentTreeCode = $params['department_tree_code'];
            $builder->where('department_tree_code', 'like', $departmentTreeCode . "%");
        }

        // status
        if (isset($params['status']) && $params['status'] != '') {
            $status = $params['status'];
            $builder->where('status', $status);
        }

        return $builder;
    }

    public static function miniGetWorkTaskQuery($user, $params)
    {
        $builder = WorkTask::query();
        if ($user->hasRole(config('system.city_admin_role_slug'))) {
            $builder = WorkTask::query();
            $builder->withCount([
                "reports as report_department_count" => function ($query) {
                    // 去重
                    $query->select(DB::raw('count(distinct(department_tree_code))'));
                }
            ]);
        } else if (isset($params['scope']) && $params['scope'] != '') {
            $scope = $params['scope'];
            // 之前是和部门直接关联，如果改成只和顶级部门关联，数据会大量减少，溶质
            if ($scope == 'create') {
                $regionTreeCodes = $user->getManagerRegionTreeCode();
                if (count($regionTreeCodes) == 0) {
                    $builder->where('id', 0);
                } else {

                    $builder->where(function ($query) use ($regionTreeCodes) {
                        foreach ($regionTreeCodes as $regionTreeCode) {
                            $query->orWhere('department_tree_code', 'like', $regionTreeCode . '%');
                        }
                    });
                }

                $builder->withCount([
                    "reports as report_department_count" => function ($query) use ($regionTreeCodes) {
                        // 去重
                        $query->select(DB::raw('count(distinct(department_tree_code))'));
                    }
                ]);
            } elseif ($scope == 'can_handle') {
                $builder->whereHas('departments', function ($query) use ($user) {
                    $query->whereRaw("'$user->department_tree_code' like concat(department_tree_code,'%')");
                });
                $builder->withCount([
                    "reports" => function ($query) use ($user) {
                        $query->where('department_tree_code', $user->department_tree_code);
                    }
                ]);
                $builder->where('status', WorkTaskConst::statusPublished);
            } elseif ($scope == 'can_see') {
                $regionTreeCodes = $user->getManagerRegionTreeCode();

                $builder->where(function ($query) use ($regionTreeCodes, $user) {
                    foreach ($regionTreeCodes as $regionTreeCode) {
                        $query->orWhere('department_tree_code', 'like', $regionTreeCode . '%');
                    }
                    $query->orWhere('user_id', $user->id);
                    $query->orWhereHas('departments', function ($query) use ($user) {
                        $query->whereRaw("'$user->department_tree_code' like concat(department_tree_code,'%')");
                    });
                });
            } else {
                throw new \Exception('没有权限');
            }
        } else {
            throw new \Exception('没有权限');
        }

        if (isset($params['title']) && $params['title'] != '') {
            $title = $params['title'];
            $builder->where('title', 'like', '%' . $title . '%');
        }

        // type_id
        if (isset($params['type_id']) && $params['type_id'] != '') {
            $typeId = $params['type_id'];
            $builder->where('type_id', $typeId);
        }

        // department_tree_code
        if (isset($params['department_tree_code']) && $params['department_tree_code'] != '') {
            $departmentTreeCode = $params['department_tree_code'];
            $builder->where('department_tree_code', 'like', $departmentTreeCode . "%");
        }

        // status
        if (isset($params['status']) && $params['status'] != '') {
            $status = $params['status'];
            $builder->where('status', $status);
        }

        // finish
        if (isset($params['finish']) && $params['finish'] != '') {
            $finish = $params['finish'];
            if ($finish) {
                $builder->whereHas('reports', function ($query) use ($user) {
                    $query->where('department_tree_code', $user->department_tree_code);
                });
            } else {
                $builder->whereDoesntHave('reports', function ($query) use ($user) {
                    $query->where('department_tree_code', $user->department_tree_code);
                });
            }
        }

        return $builder;
    }

    public static function miniGetWorkTaskReportQuery($user, $params)
    {
        $workTaskBuilder = ScopeQueryImpl::miniGetWorkTaskQuery($user, ['scope' => 'can_see']);
        $workTaskId      = $params['work_task_id'] ?? 0;

        $workTask = $workTaskBuilder->where('id', $workTaskId)->first();
        if (!$workTask) {
            throw new \Exception('任务不存在，缺少work_task_id');
        }
        $workTaskReportBuilder = $workTask->reports();

        if ($user->hasRole(config('system.city_admin_role_slug'))) {
        } else {
            if (isset($params['scope']) && $params['scope'] != '') {
                $scope = $params['scope'];
                if ($scope == 'can_see') {
                    $regionTreeCodes = $user->getManagerRegionTreeCode();
                    $workTaskReportBuilder->where(function ($query) use ($regionTreeCodes, $user) {
                        foreach ($regionTreeCodes as $regionTreeCode) {
                            $query->orWhere('department_tree_code', 'like', $regionTreeCode . '%');
                        }
                        $query->orWhere('user_id', $user->id);
                    });
                } elseif ($scope == "my_department") {
                    $workTaskReportBuilder->where('department_tree_code', $user->department_tree_code);
                } else if ($scope == 'create') {
                    $workTaskReportBuilder->where('user_id', $user->id);
                } else {
                    throw new \Exception('没有权限');
                }
            } else {
                throw new \Exception('没有权限 scope');
            }
        }


        // department_tree_code
        if (isset($params['department_tree_code']) && $params['department_tree_code'] != '') {
            $departmentTreeCode = $params['department_tree_code'];
            $workTaskReportBuilder->where('department_tree_code', 'like', $departmentTreeCode . "%");
        }

        // name
        if (isset($params['name']) && $params['name'] != '') {
            $name = $params['name'];
            $workTaskReportBuilder->where('user_name', 'like', '%' . $name . '%');
        }

        return $workTaskReportBuilder;
    }

    public static function miniGetInformQuery($user, $params)
    {
        $builder = Inform::query();
        $builder->where("template", ArticleConst::templateInform);

        if ($user->hasRole(config('system.city_admin_role_slug'))) {
        } else if (isset($params['scope']) && $params['scope'] != '') {
            $scope = $params['scope'];
            if ($scope == 'create') {
                $regionTreeCodes = $user->getManagerRegionTreeCode();
                if (count($regionTreeCodes) == 0) {
                    throw new \Exception('没有权限');
                }
                $builder->where(function ($query) use ($regionTreeCodes) {
                    foreach ($regionTreeCodes as $regionTreeCode) {
                        $query->orWhere('department_tree_code', 'like', $regionTreeCode . '%');
                    }
                });
            } elseif ($scope == 'can_handle') {
                $builder->whereHas('departments', function ($query) use ($user) {
                    $query->whereRaw("'$user->department_tree_code' like concat(department_tree_code,'%')");
                });
                $builder->where('status', ArticleConst::statusPublished);
            } elseif ($scope == 'can_see') {
                $regionTreeCodes = $user->getManagerRegionTreeCode();
                $builder->where(function ($query) use ($regionTreeCodes, $user) {
                    foreach ($regionTreeCodes as $regionTreeCode) {
                        $query->orWhere('department_tree_code', 'like', $regionTreeCode . '%');
                    }
                    $query->orWhere('user_id', $user->id);
                    $query->orWhereHas('departments', function ($query) use ($user) {
                        $query->whereRaw("'$user->department_tree_code' like concat(department_tree_code,'%')");
                    });
                });
            } else {
                throw new \Exception('没有权限');
            }
        } else {
            throw new \Exception('没有权限');
        }

        // title
        if (isset($params['title']) && $params['title'] != '') {
            $title = $params['title'];
            $builder->where('title', 'like', '%' . $title . '%');
        }

        // status
        if (isset($params['status']) && $params['status'] != '') {
            $status = $params['status'];
            $builder->where('status', $status);
        }

        // department_tree_code
        if (isset($params['department_tree_code']) && $params['department_tree_code'] != '') {
            $departmentTreeCode = $params['department_tree_code'];
            $builder->where('department_tree_code', 'like', $departmentTreeCode . "%");
        }


        return $builder;
    }


    public static function miniGetSurveyQuery($user, $params)
    {
        $builder = Survey::query();
        if ($user) {
            $builder->where('user_type', SurveyConst::user_type_platform);
            $builder->whereHas('departments', function ($query) use ($user) {
                $query->whereRaw("'$user->department_tree_code' like concat(department_tree_code,'%')");
            });
        } else {
            $builder->where('user_type', SurveyConst::user_type_anybody);
        }

        // status
        if (isset($params['status']) && $params['status'] != '') {
            $statusStr = $params['status'];
            $status    = explode(',', $statusStr);
            $builder->whereIn('status', $status);
        } else {
            if ($user) {
                $builder->whereIn('status', [SurveyConst::statusRunning, SurveyConst::statusFinished]);
            }
        }

        // finish
        if (isset($params['finish']) && $params['finish'] != '' && $user) {
            $finish = $params['finish'];
            if ($finish) {
                $builder->whereHas('submitForms', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                });
            } else {
                $builder->whereDoesntHave('submitForms', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                });
            }
        }


        return $builder;
    }


    public static function miniGetRecallTaskSoldierQuery($user, $params)
    {
        return self::adminGetRecallTaskSoldierQuery($user, $params);
    }

    public static function miniGetRecallTaskQuery($user, $params)
    {
        return self::adminGetRecallTaskQuery($user, $params);
    }


    public static function miniGetOutNoticeTaskQuery($user, $params)
    {
        $builder = OutNoticeTask::query();
        if ($user->hasRole(config('system.city_admin_role_slug'))) {
        } else {
            $builder->where('user_id', $user->id);
        }

        return $builder;
    }


    public static function miniGetNotificationQuery($user, $params)
    {
        return self::adminGetNotificationQuery($user, $params);
    }

    public static function miniGetMissionPersonQuery($user, $params)
    {
        return self::adminGetMissionPersonQuery($user, $params);
    }

    // =======================================================================================

    public static function adminGetWorkTaskQuery($user, $params)
    {
        return self::miniGetWorkTaskQuery($user, $params);
    }

    public static function adminGetWorkTaskReportQuery($user, $params)
    {
        return self::miniGetWorkTaskReportQuery($user, $params);
    }

    public static function adminGetInformQuery($user, $params)
    {
        return self::miniGetInformQuery($user, $params);
    }

    public static function adminGetReportQuery($user, $params)
    {
        return self::miniGetReportQuery($user, $params);
    }


    public static function adminGetRecallTaskSoldierQuery($user, $params)
    {
        $taskBuilder = self::adminGetRecallTaskQuery($user, $params);
        if (isset($params['task_id'])) {
            $taskId = $params['task_id'];
            $task   = $taskBuilder->where('id', $taskId)->first();
            if (!$task) {
                return RecallTaskSoldier::query()->where('id', 0);
            }
            $builder = $task->soldiers();
            $builder->where('task_id', $taskId);
        } else {
            $builder = RecallTaskSoldier::query()->whereHas('recallTask', function ($query) use ($user) {
                $query->where('disabled', 0);
            });
        }

        if ($user->hasRole(config('system.city_admin_role_slug'))) {
        } else {
            $builder->where(function ($query) use ($user) {
                $regionTreeCodes = $user->getManagerRegionTreeCode();
                if (count($regionTreeCodes) > 0) {
                    foreach ($regionTreeCodes as $regionTreeCode) {
                        $query->orWhere('department_tree_code', 'like', $regionTreeCode . '%');
                    }
                } else {
                    $query->where('id', 0);
                }
            });
        }

        // face_complete
        if (isset($params['face_complete']) && $params['face_complete'] != '') {
            $faceComplete = $params['face_complete'];
            $builder->where('face_complete', $faceComplete);
        }
        // body_examination_complete
        if (isset($params['body_examination_complete']) && $params['body_examination_complete'] != '') {
            $bodyExaminationComplete = $params['body_examination_complete'];
            $builder->where('body_examination_complete', $bodyExaminationComplete);
        }
        // political_examination_complete
        if (isset($params['political_examination_complete']) && $params['political_examination_complete'] != '') {
            $politicalExaminationComplete = $params['political_examination_complete'];
            $builder->where('political_examination_complete', $politicalExaminationComplete);
        }
        // transport_complete
        if (isset($params['transport_complete']) && $params['transport_complete'] != '') {
            $transportComplete = $params['transport_complete'];
            $builder->where('transport_complete', $transportComplete);
        }

        // keyword
        if (isset($params['keyword']) && $params['keyword'] != '') {
            $keyword = $params['keyword'];
            $builder->where(function ($query) use ($keyword) {
                $query->where('name', 'like', '%' . $keyword . '%');
                $query->orWhere('id_card', 'like', '%' . $keyword . '%');
                $query->orWhere('contact', 'like', '%' . $keyword . '%');
                $query->orWhere('family_contact', 'like', '%' . $keyword . '%');
            });
        }

        return $builder;
    }

    public static function adminGetRecallTaskQuery($user, $params)
    {
        $builder = RecallTask::query();
        if ($user->hasRole(config('system.city_admin_role_slug'))) {
        } else {
        }

        // name
        if (isset($params['name']) && $params['name'] != "") {
            $builder->where('name', 'like', "%{$params['name']}%");
        }
        return $builder;
    }

    public static function adminGetUserQuery($user, $params)
    {
        $builder = User::query();
        if ($user->hasRole(config('system.city_admin_role_slug'))) {
        } else {
            $regionTreeCodes = $user->getManagerRegionTreeCode();
            if (count($regionTreeCodes) > 0) {
                $builder->where(function ($query) use ($regionTreeCodes) {
                    foreach ($regionTreeCodes as $regionTreeCode) {
                        $query->orWhere('department_tree_code', 'like', $regionTreeCode . '%');
                    }
                });
            } else {
                $builder->where('id', 0);
            }
        }

        if (isset($params['name']) && $params['name'] != '') {
            $builder->where('name', 'like', '%' . $params['name'] . '%');
        }

        //phone
        if (isset($params['phone']) && $params['phone'] != '') {
            $builder->where('phone', $params['phone']);
        }

        //department_code
        if (isset($params['department_code']) && $params['department_code'] != '') {
            $departmentCode = $params['department_code'];
            $department     = Region::query()->where('code', $departmentCode)->first();
            if ($department) {
                $builder->where('department_tree_code', 'like', $department->tree_code . '%');
            } else {
                $builder->where('id', 0);
            }
        }


        // business_type_id
        if (isset($params['business_type_id']) && $params['business_type_id'] != '') {
            $builder->where('business_type_id', $params['business_type_id']);
        }

        // sex
        if (isset($params['sex']) && $params['sex'] != '') {
            $builder->where('sex', $params['sex']);
        }

        //role_id
        if (isset($params['role_id']) && $params['role_id'] != '') {
            $builder->whereHas('roles', function ($query) use ($params) {
                $query->where('role_id', $params['role_id']);
            });
        }

        // status
        if (isset($params['status']) && $params['status'] != '') {
            $builder->where('status', $params['status']);
        }

        // keyword
        if (isset($params['keyword']) && $params['keyword'] != '') {
            $keyword = $params['keyword'];
            $builder->where(function ($query) use ($keyword) {
                $query->where('name', 'like', '%' . $keyword . '%');
                $query->orWhere('phone', 'like', '%' . $keyword . '%');
            });
        }

        // faced
        if (isset($params['faced']) && $params['faced'] != '') {
            $faced = $params['faced'];
            $builder->where('faced', $faced);
        }

        // activated
        if (isset($params['activated']) && $params['activated'] != '') {
            $activated = $params['activated'];
            $builder->where('activated', $activated);
        }

        return $builder;
    }


    /**
     * 授权码
     */
    public static function adminGetInvitationCodeQuery($user, $params)
    {
        $builder = InvitationCode::query();
        $builder->orderBy("created_at", "desc");

        $builder->with([
            'department',
        ]);

        if (isset($params) && isset($params['department_code'])) {
            $code = $params['department_code'];
            $builder->where(function ($query) use ($code) {
                $query->where('department_code', $code)->orWhere('district_code', $code)
                    ->orWhere('street_code', $code)->orWhere('village_code', $code);
            });
        } else {
            $managerRegionTreeCode = $user->getManagerRegionTreeCode();
            if (count($managerRegionTreeCode) > 0) {
                $builder->where(function ($query) use ($managerRegionTreeCode) {
                    foreach ($managerRegionTreeCode as $regionTreeCode) {
                        $query->orWhere('department_tree_code', 'like', $regionTreeCode . '%');
                    }
                });
            } else {
                $builder->where('id', 0);
            }
        }

        //disabled
        if (isset($params['disabled']) && $params['disabled'] !== '') {
            $disabled = $params['disabled'];
            $builder->where('disabled', $disabled);
        }

        if (isset($params['code']) && $params['code'] !== '') {
            $code = $params['code'];
            $builder->where('code', $code);
        }

        return $builder;
    }


    public static function adminGetArticleCountQuery($user, $params)
    {
        $builder = Article::query();
        if ($user->hasRole(config('system.city_admin_role_slug'))) {
        } else {
            $regionTreeCodes = $user->getManagerRegionTreeCode();
            if (count($regionTreeCodes) > 0) {
                $builder->where(function ($query) use ($regionTreeCodes) {
                    foreach ($regionTreeCodes as $regionTreeCode) {
                        $query->orWhere('department_tree_code', 'like', $regionTreeCode . '%');
                    }
                });
            } else {
                $builder->where('id', 0);
            }
        }
        return $builder;
    }

    public static function adminGetSurveyQuery($user, $params)
    {
        $builder = Survey::query();
        if ($user->hasRole(config('system.city_admin_role_slug'))) {
        } else {
        }

        // user_type
        if (isset($params['user_type']) && $params['user_type'] != '') {
            $builder->where('user_type', $params['user_type']);
        }

        // status
        if (isset($params['status']) && $params['status'] != '') {
            $builder->where('status', $params['status']);
        }

        return $builder;
    }

    public static function adminGetSurveyFormQuery($user, $params)
    {
        $builder  = SurveyForm::query();
        $surveyId = $params['survey_id'] ?? 0;
        $builder->where('survey_id', $surveyId);
        // user_name
        if (isset($params['user_name']) && $params['user_name'] != '') {
            $userName = $params['user_name'];
            $builder->where('user_name', 'like', '%' . $userName . '%');
        }
        // department_tree_code
        if (isset($params['department_tree_code']) && $params['department_tree_code'] != '') {
            $departmentTreeCode = $params['department_tree_code'];
            $builder->where('department_tree_code', 'like', $departmentTreeCode . '%');
        }
        return $builder;
    }


    public static function adminGetNotificationQuery($user, $params)
    {
        $builder     = SystemNotification::query();
        $regionCodes = $user->getManagerRegionCode();


        $builder->where(function ($query) use ($regionCodes, $user) {
            if (count($regionCodes) > 0) {
                $query->where(function ($query) use ($regionCodes) {

                    $query->where(function ($query) use ($regionCodes) {
                        $query->orWhere(function ($query) use ($regionCodes) {
                            foreach ($regionCodes as $regionCode) {
                                $query->orWhere('department_code', $regionCode);
                            }
                        });
                    });
                    $query->where('permission', 1);

                });
            }

            $query->orWhere('user_id', $user->id);

            $query->orWhere(function ($query) use ($user) {
                $query->where('department_code', $user->department_code)->where('permission', 0);
            });
        });

        if (!$user->hasPermission([PermissionSlugConst::admin_report_reply])) {
            $builder->whereNotIn('type', [SystemNotificationConst::reportNew]);
        }

        // unread
        if (isset($params['status']) && $params['status'] != "") {
            $status = $params['status'];
            if ($status == 'unread') {
                $builder->whereDoesntHave('reads', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                });
            } elseif ($status == 'read') {
                $builder->whereHas('reads', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                });
            }
        }

        return $builder;
    }

    public static function adminGetStatisticsUserPointQueryItems($user, $params)
    {
        $builder = ScopeQueryImpl::adminGetUserQuery($user, $params);
        $builder->select([
            'id',
            'name',
            'department_code',
            'department_name',
            'district_name',
            'phone',
        ]);
        $builder->addSelect([
            'points' => function ($query) use ($params) {
                if (isset($params['stage_id']) && $params['stage_id'] != '') {
                    $query->where('stage_id', $params['stage_id']);
                }
                $query->select(DB::raw('sum(point)'))
                    ->from('user_points')
                    ->whereColumn('user_points.user_id', 'users.id');
            }
        ]);
        $builder->orderBy('points', 'desc');
        $builder->orderByDesc("id");
        $items = $builder->paginate(pageSize());
        return $items;
    }


    public static function adminGetStatisticsUserCallQueryItems($user, $params)
    {
        $userBuilder = ScopeQueryImpl::adminGetUserQuery($user, $params);
        $userBuilder->addSelect([
            'recall_task_soldier_count' => function ($query) use ($params) {
                $query->select(DB::raw('count(*)'))->from('user_call_logs')
                    ->whereColumn('user_call_logs.user_id', 'users.id')
                    ->where('call_type', UserConst::callTypeRecallTaskSoldier);
                if (isset($params['start_at']) && $params['start_at'] != '') {
                    $query->where('created_at', '>=', $params['start_at']);
                }
                if (isset($params['end_at']) && $params['end_at'] != '') {
                    $query->where('created_at', '<=', Carbon::parse($params['end_at'])->endOfDay());
                }
            },
            'teen_soldier_count'        => function ($query) use ($params) {
                $query->select(DB::raw('count(*)'))->from('user_call_logs')
                    ->whereColumn('user_call_logs.user_id', 'users.id')
                    ->where('call_type', UserConst::callTypeTeenSoldier);
                if (isset($params['start_at']) && $params['start_at'] != '') {
                    $query->where('created_at', '>=', $params['start_at']);
                }
                if (isset($params['end_at']) && $params['end_at'] != '') {
                    $query->where('created_at', '<=', Carbon::parse($params['end_at'])->endOfDay());
                }
            },
        ]);
        $userBuilder->orderByRaw('recall_task_soldier_count + teen_soldier_count desc');
//        $userBuilder->orderByRaw('recall_task_soldier_count + teen_soldier_count desc');
        $items = $userBuilder->paginate(pageSize());
        return $items;
    }


    public static function adminGetStatisticsUserCourseQuery($user, $params)
    {
        $builder = StatisticsUserCourse::query();
        // stage_id
        if (isset($params['stage_id']) && $params['stage_id'] != '') {
            $builder->where('stage_id', $params['stage_id']);
        }
        // department_code
        if (isset($params['department_code']) && $params['department_code'] != '') {
            $department_code = $params['department_code'];
            $department      = Region::query()->where('code', $department_code)->first();
            if ($department) {
                $department_tree_code = $department->tree_code;
                $builder->where('department_tree_code', 'like', $department_tree_code . '%');
            }
        } else {
            $managerRegionTreeCodes = $user->getManagerRegionTreeCode();
            $builder->where(function ($query) use ($managerRegionTreeCodes) {
                foreach ($managerRegionTreeCodes as $code) {
                    $query->orWhere('department_tree_code', "like", $code . '%');
                }
            });
        }
        $builder->orderByDesc('study_count');
        return $builder;
    }

    public static function adminGetStatisticsDepartmentCourseQueryItems($user, $params)
    {
        $builder = Region::query()->whereNotIn('name', RegionConst::excludeRegionName);
        // p_code
        if (isset($params['department_code']) && $params['department_code'] != '') {
            $p_code = $params['department_code'];
            $builder->where('p_code', $p_code);
        } else {
            $managerRegionTreeCodes = $user->getManagerRegionTreeCode();
            $builder->where(function ($query) use ($managerRegionTreeCodes) {
                foreach ($managerRegionTreeCodes as $code) {
                    $query->orWhere('tree_code', "like", $code . '%');
                }
            });

        }
        $builder->select(['code', 'name', 'p_code']);
        $builder->addSelect([
            'total_count' => function ($query) use ($params) {
                if (isset($params['stage_id']) && $params['stage_id'] != '') {
                    $query->where('stage_id', $params['stage_id']);
                }
                $query->select(DB::raw('count(distinct user_id)'))
                    ->from('statistics_user_courses')
                    ->whereColumn('statistics_user_courses.department_tree_code', 'like', DB::raw("concat(regions.tree_code,'%')"));
            },
            'study_count' => function ($query) use ($params) {
                if (isset($params['stage_id']) && $params['stage_id'] != '') {
                    $query->where('stage_id', $params['stage_id']);
                }
                $query->select(DB::raw('count(distinct user_id)'))
                    ->from('statistics_user_courses')
                    ->whereColumn('statistics_user_courses.department_tree_code', 'like', DB::raw("concat(regions.tree_code,'%')"))
                    ->where(DB::raw("study_count > need_study_count"));
            },
        ]);
        $items = $builder->paginate(pageSize());


        foreach ($items as $item) {
            // 未完成
            $item->unfinished_count = $item->total_count - $item->study_count;
        }
        return $items;
    }

    public static function adminGetStatisticsCourseQuery($user, $params)
    {
        $builder = StatisticsCourse::query();

        if (isset($params['stage_id']) && $params['stage_id'] != '') {
            $builder->where('stage_id', $params['stage_id']);
        }
        if (isset($params['course_id']) && $params['course_id'] != '') {
            $builder->where('course_id', $params['course_id']);
        }
        //chapter_id
        if (isset($params['chapter_id']) && $params['chapter_id'] != '') {
            $builder->where('chapter_id', $params['chapter_id']);
        }
        // sub_chapter_id
        if (isset($params['sub_chapter_id']) && $params['sub_chapter_id'] != '') {
            $builder->where('sub_chapter_id', $params['sub_chapter_id']);
        }
        // type
        if (isset($params['type']) && $params['type'] != '') {
            $builder->where('type', $params['type']);
        }
        return $builder;
    }

    public static function adminGetStatisticsUserPaperQueryItems($user, $params)
    {
        $builder = User::query();

        $paperRoundTable = DB::table('paper_rounds');

        // stage_id
        if (isset($params['stage_id']) && $params['stage_id'] != '') {
            $paperRoundTable->where('stage_id', $params['stage_id']);
        }

        if (isset($params['paper_id']) && $params['paper_id'] != '') {
            $paperRoundTable->where('paper_id', $params['paper_id']);
        }

        $paperRoundTable->groupBy('user_id');
        $paperRoundTable->select(['user_id', DB::raw('max(score) as max_score')]);


        $builder->leftJoin(DB::raw('(' . $paperRoundTable->toRawSql() . ' ) as pr'), function ($join) {
            $join->on('users.id', '=', 'pr.user_id');
        });

        if (isset($params['department_code']) && $params['department_code'] != '') {
            $p_code     = $params['department_code'];
            $department = Region::query()->where('code', $p_code)->first();
            if ($department) {
                $department_tree_code = $department->tree_code;
                $builder->where('users.department_tree_code', 'like', $department_tree_code . '%');
            }
        } else {
            $managerRegionTreeCodes = $user->getManagerRegionTreeCode();
            $builder->where(function ($query) use ($managerRegionTreeCodes) {
                foreach ($managerRegionTreeCodes as $code) {
                    $query->orWhere('users.department_tree_code', "like", $code . '%');
                }
            });
        }


        // is_simulate
        if (isset($params['is_simulate']) && $params['is_simulate'] != '') {
            $isSimulate = $params['is_simulate'];
            $builder->where('pr.is_simulate', $isSimulate);
        }

        // is_pass
        if (isset($params['is_pass']) && $params['is_pass'] != '') {
            $isPass = $params['is_pass'];
            if ($isPass) {
                $builder->whereHas('paperRounds', function ($query) use ($isPass) {
                    $query->where('is_pass', 1);
                });
            } else {
                $builder->whereDoesntHave('paperRounds', function ($query) use ($isPass) {
                    $query->where('is_pass', 1);
                });
            }
        }
        $builder->select([
            'users.id',
            'users.name',
            'users.district_name',
            'users.department_code',
            'users.department_name',
            'pr.max_score',
        ]);


        $items = $builder->paginate(pageSize());

        foreach ($items as $item) {
            $paperRoundBuilder = PaperRound::query();
            // paper_id
            if (isset($params['paper_id']) && $params['paper_id'] != '') {
                $paperRoundBuilder->where('paper_id', $params['paper_id']);
            }
            // stage_id
            if (isset($params['stage_id']) && $params['stage_id'] != '') {
                $paperRoundBuilder->where('stage_id', $params['stage_id']);
            }
            // user_id
            $paperRoundBuilder->where('user_id', $item->id);
            $paperRound    = $paperRoundBuilder->where('score', $item->max_score)->first();
            $item->end_at  = $paperRound->end_at ?? '';
            $item->is_pass = $paperRound->is_pass ?? '';
        }
        return $items;
    }

    public static function adminGetStatisticsDepartmentPaperQueryItems($user, $params)
    {
        $builder = Region::query()->whereNotIn('name', RegionConst::excludeRegionName);
        // p_code
        if (isset($params['department_code']) && $params['department_code'] != '') {
            $p_code = $params['department_code'];
            if ($p_code == 'c1101') {
                $builder->where(function ($query) use ($p_code) {
                    $query->where('p_code', $p_code)->orWhere('code', $p_code);
                });
            } else {
                $builder->where('p_code', $p_code);
            }
        } else {
            $managerRegionTreeCodes = $user->getManagerRegionTreeCode();
            $builder->where(function ($query) use ($managerRegionTreeCodes) {
                foreach ($managerRegionTreeCodes as $code) {
                    $query->orWhere('tree_code', "like", $code . '%');
                }
            });
        }
        $builder->select(['code', 'name', 'p_code']);
        $builder->addSelect([
            'total_count'    => function ($query) use ($params) {
                $query->select(DB::raw('count(*)'))
                    ->from('users')
                    ->whereNull('users.deleted_at')
                    ->whereRaw('users.department_tree_code like concat(regions.tree_code,"%")');
            },
            'finished_count' => function ($query) use ($params) {
                // stage_id
                if (isset($params['stage_id']) && $params['stage_id'] != '') {
                    $query->where('stage_id', $params['stage_id']);
                }
                // paper_id
                if (isset($params['paper_id']) && $params['paper_id'] != '') {
                    $query->where('paper_id', $params['paper_id']);
                }
                $query->select(DB::raw('count(*)'))
                    ->from('paper_rounds')
                    ->whereNull('paper_rounds.deleted_at')
                    ->whereRaw('paper_rounds.department_tree_code like concat(regions.tree_code,"%")')
                    ->where('status', PaperConst::roundStatusFinished);
            },
            'passed_count'   => function ($query) use ($params) {
                // stage_id
                if (isset($params['stage_id']) && $params['stage_id'] != '') {
                    $query->where('stage_id', $params['stage_id']);
                }
                // paper_id
                if (isset($params['paper_id']) && $params['paper_id'] != '') {
                    $query->where('paper_id', $params['paper_id']);
                }
                $query->select(DB::raw('count(*)'))
                    ->from('paper_rounds')
                    ->whereNull('paper_rounds.deleted_at')
                    ->whereRaw('paper_rounds.department_tree_code like concat(regions.tree_code,"%")')
                    ->where('is_pass', 1);
            },
        ]);
        $items = $builder->paginate(pageSize());

        return $items;
    }

    /**
     * Begin querying the model.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function adminGetMissionPersonQuery($user, $params)
    {
        $builder     = MissionPerson::query();
        $regionCodes = $user->getManagerRegionCode();
        if (count($regionCodes) == 0) {
            $builder->where('id', 0);
        }
        if ($user->hasRole(config('system.city_admin_role_slug'))) {

        } else if ($user->hasRole(RoleConst::district_admin)) {
            if (count($regionCodes) == 1) {
                $builder->where('mission_persons.district_code', $regionCodes[0]);
            } elseif (count($regionCodes) > 1) {
                $builder->whereIn('mission_persons.district_code', $regionCodes);
            }
        } else if ($user->hasRole(RoleConst::street_admin)) {
            if (count($regionCodes) == 1) {
                $builder->where('mission_persons.street_code', $regionCodes[0]);
            } elseif (count($regionCodes) > 1) {
                $builder->whereIn('mission_persons.street_code', $regionCodes);
            }
        } else if ($user->hasRole(RoleConst::school_admin)) {
            if (count($regionCodes) == 1) {
                $builder->where('mission_persons.school_code', $regionCodes[0]);
            } elseif (count($regionCodes) > 1) {
                $builder->whereIn('mission_persons.school_code', $regionCodes);
            }
        }

        // type
        if (isset($params['type']) && $params['type']) {
            $builder->where('mission_persons.type', $params['type']);
        }

        if (isset($params['name']) && $params['name']) {
            $builder->where('mission_persons.name', 'like', "%{$params['name']}%");
        }

        // intention
        if (isset($params['intention']) && $params['intention']) {
            $intention = $params['intention'];
            if (in_array($intention, [MissionPersonConst::personIntentionYes, MissionPersonConst::personIntentionNo])) {
                $builder->where('mission_persons.intention', $params['intention']);
            }
            if ($intention == 2) {
                $builder->whereNull('mission_persons.intention_at');
            }
        }

        // promote_compare_result
        if (isset($params['promote_compare_result']) && $params['promote_compare_result']) {
            $builder->where('mission_persons.promote_compare_result', $params['promote_compare_result']);
        }

        // mission_physical_examination_id
        if (isset($params['mission_physical_examination_id']) && $params['mission_physical_examination_id']) {
            $builder->where('mission_persons.mission_physical_examination_id', $params['mission_physical_examination_id']);
        }


        // mission_id
        if (isset($params['mission_id']) && $params['mission_id']) {
            $builder->where('mission_persons.mission_id', $params['mission_id']);
        }

        $builder = self::adminGetMissionPersonWithSceneQuery($builder, $params);

        return $builder;
    }

    public static function adminGetMissionPersonWithSceneQuery($builder, $params)
    {
        $sceneStr = $params['scene'] ?? null;

        $sceneArr = explode(',', $sceneStr);

        if (count($sceneArr) == 0) {
            return $builder;
        }
        for ($i = 0; $i < count($sceneArr); $i++) {
            $scene = $sceneArr[$i];
            if ($scene == MissionConst::missionPersonListSceneMissionPhysicalExaminationAddPerson) {
                // 体检计划上报选择人员
                $builder->where('mission_persons.sign', MissionPersonConst::personSignStatusSign)->whereNull("mission_persons.mission_physical_examination_id");
            } else if ($scene == MissionConst::missionPersonListSceneMissionPhysicalCheckResult) {
                // 初次体检结果设置页面
                $builder->leftJoin('mission_physical_examinations', 'mission_physical_examinations.id', '=', 'mission_persons.mission_physical_examination_id');
                $builder->whereNotNull('mission_persons.mission_physical_examination_id');
                $builder->where('mission_physical_examinations.status', MissionConst::missionPhysicalExaminationStatusSubmitted);
            } else if ($scene == MissionConst::missionPersonListSceneMissionPhysicalRecheckResult) {
                //  体检复查结果设置页面
                $builder->where('mission_persons.physical_need_recheck', 1);
            } else if ($scene == MissionConst::missionPersonListSceneMissionPhysicalExaminationRecheckAddPerson) {
                // 复查添加人员
                $builder->where('mission_persons.physical_check', MissionPersonConst::personPhysicalCheckNo);
                $builder->where('mission_persons.physical_need_recheck', -1);
            } else if ($scene == MissionConst::missionPersonListSceneMissionPhysicalSpotCheckPersonList) {
                // 抽查列表
                $builder->where('mission_persons.physical_need_spot_check', 1);
            } else if ($scene == MissionConst::missionPersonListSceneMissionPoliticalExamPerson) {
                // 政考人员列表
                $builder->where('mission_persons.political_examination', '!=', -1);
            } else if ($scene == MissionConst::missionPersonListSceneMissionPreStorePerson) {
                // 预存人员列表
                $builder->where('mission_persons.pre_type', '!=', -1);
            } else if ($scene == MissionConst::missionPersonListSceneMissionEducationPerson) {
                // 教育
                $builder->where('mission_persons.education_result', '!=', -1);
            }
        }
        return $builder;
    }

    public static function adminGetRegionWithCodeQuery($user, $params)
    {
        $builder = Region::query();

        if (isset($params['name']) && $params['name']) {
            $builder->where('regions.name', 'like', '%' . $params['name'] . '%');
        }

        // level
        if (isset($params['level']) && $params['level']) {
            $builder->where('regions.level', '=', $params['level']);
        }

        // pid
        if (isset($params['p_code'])) {
            $builder->where('regions.p_code', '=', $params['p_code']);
        } else {
            $user        = auth()->user();
            $regionCodes = $user->getManagerRegionCode();
            if (count($regionCodes) > 0) {
                $builder->where(function ($query) use ($regionCodes) {
                    foreach ($regionCodes as $regionCode) {
                        $query->orWhere('regions.code', $regionCode);
                    }
                });
            }
        }
        return $builder;
    }


    public static function adminGetRegionWithTreeCodeQuery($user, $params)
    {
        $builder = Region::query();

        if (isset($params['name']) && $params['name']) {
            $builder->where('regions.name', 'like', '%' . $params['name'] . '%');
        }

        // level
        if (isset($params['level']) && $params['level']) {
            $builder->where('regions.level', '=', $params['level']);
        }

        // depth
        if (isset($params['depth']) && $params['depth']) {
            $builder->where('regions.depth', '=', $params['depth']);
        }

        // pid
        if (isset($params['p_code'])) {
            $builder->where('regions.p_code', '=', $params['p_code']);
        } else {
            $regionCodes = $user->getManagerRegionTreeCode();
            if (count($regionCodes) > 0) {
                $builder->where(function ($query) use ($regionCodes) {
                    foreach ($regionCodes as $regionCode) {
                        $query->orWhere('regions.tree_code', "like", $regionCode . "%");
                    }
                });
            }
        }
        return $builder;
    }

    public static function miniGetRegionWithTreeCodeQuery($user, $params)
    {
        return self::adminGetRegionWithTreeCodeQuery($user, $params);
    }


    public static function adminGetMissionRegionSettingQuery($user, $params)
    {
        $missionId = $params["mission_id"] ?? 0;
        $builder   = ScopeQueryImpl::adminGetRegionWithTreeCodeQuery($user, $params);

        $builder->leftJoin('mission_region_settings', function ($query) use ($missionId) {
            $query->on('mission_region_settings.code', '=', 'regions.code');
            $query->where('mission_region_settings.mission_id', $missionId);
        });

        $builder->where('regions.name', "!=", "学校");

        if ($user->hasRole(RoleConst::district_admin)) {
            $builder->whereIn("regions.level", [RegionConst::levelDistrict, RegionConst::levelStreet]);
        } else if ($user->hasRole(RoleConst::city_admin)) {
            $builder->whereIn("regions.level", [RegionConst::levelSchool, RegionConst::levelDistrict]);
        } else {
            $builder->whereIn('regions.level', [RegionConst::levelDistrict, RegionConst::levelStreet, RegionConst::levelSchool]);
        }

        // status
        if (isset($params['status']) && $params['status']) {
            $status = $params['status'];
            if ($status == 1) {
                $builder = $builder->whereNotNull("task_num_at");
            } elseif ($status == -1) {
                $builder = $builder->whereNull("task_num_at");
            }
        }

        // level
        if (isset($params['level']) && $params['level']) {
            $builder = $builder->where('regions.level', $params['level']);
        }

        // department_code
        if (isset($params['department_code']) && $params['department_code']) {
            $builder = $builder->where('regions.tree_code', "like", '%' . $params['department_code'] . "-%");
        }

        $builder->select([
            'mission_region_settings.id',
            'regions.code',
            'regions.name',
            'regions.level',
            'mission_region_settings.task_num',
            'mission_region_settings.task_graduate_num',
            'mission_region_settings.before_graduate_guide_ratio',
            'mission_region_settings.graduate_guide_ratio',
            'mission_region_settings.physical_examination_station_rate',
        ]);


        return $builder;
    }

    public static function adminGetMissionSignPersonQuery($user, $params)
    {
        $builder = MissionSignPerson::query();

        if ($user->hasRole(RoleConst::district_admin)) {
            $regionCodes = $user->getManagerRegionCode();
            if (count($regionCodes) == 0) {
                $builder->where('id', 0);
            }
            $builder->where('district_code', $regionCodes[0]);
        } elseif ($user->hasRole(RoleConst::city_admin)) {

        } else {
            $builder->where('id', 0);
        }

        if (isset($params['name']) && $params['name']) {
            $builder->where('name', 'like', "%{$params['name']}%");
        }

        if (isset($params['status']) && $params['status']) {
            $builder->where('status', $params['status']);
        }

        // id_card
        if (isset($params['id_card']) && $params['id_card']) {
            $builder->where('id_card', 'like', "%{$params['id_card']}%");
        }

        // mission_id
        if (isset($params['mission_id']) && $params['mission_id']) {
            $builder->where('mission_id', $params['mission_id']);
        }

        return $builder;
    }

    public static function adminGetMissionPhysicalExaminationQuery($user, $params)
    {
        $builder = MissionPhysicalExamination::query();

        // 添加 LEFT JOIN 统计人员数量
        $builder->leftJoin('mission_persons', 'mission_physical_examinations.id', '=', 'mission_persons.mission_physical_examination_id');

        if ($user->hasRole(RoleConst::district_admin)) {
            $managerRegion = $user->managerRegion()->where('level', RegionConst::levelDistrict)->first();
            $builder->where('mission_physical_examinations.district_code', $managerRegion->code ?? '');
        } else if ($user->hasRole(RoleConst::city_admin)) {
        } else if ($user->hasRole(RoleConst::hospital_admin)) {
            $managerRegion = $user->managerRegion()->where('level', RegionConst::levelHospital)->first();
            $builder->where('mission_physical_examinations.hospital_code', $managerRegion->code ?? '');
        }

        if (isset($params['name']) && $params['name']) {
            $builder->where('mission_physical_examinations.name', 'like', "%{$params['name']}%");
        }

        // mission_id
        if (isset($params['mission_id']) && $params['mission_id']) {
            $builder->where('mission_physical_examinations.mission_id', $params['mission_id']);
        }

        // 添加 person_count 字段统计
        $builder->select([
            'mission_physical_examinations.*',
            DB::raw('COUNT(mission_persons.id) as person_count')
        ])->groupBy('mission_physical_examinations.id');

        return $builder;
    }
}
