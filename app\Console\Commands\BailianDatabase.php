<?php

namespace App\Console\Commands;

use App\Http\Consts\BailianFileConst;
use App\Http\Services\Common\AlibabaService;
use App\Models\Attachment;
use App\Models\ConsultFile;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Stream;
use Illuminate\Console\Command;

class BailianDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:bailian-database';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->handleIndexStatusConsultFiles();
        $this->handleIndexConsultFiles();
        $this->handleUploadParseConsultFiles();
        $this->handleUploadConsultFiles();
    }

    public function handleIndexStatusConsultFiles()
    {
        $alibabaService = new AlibabaService();
        $consultFile    = ConsultFile::query()->where('index_status', BailianFileConst::index_status_running)->first();
        if ($consultFile) {
            $this->info("index 文件id:{$consultFile->id}");
            $resp = $alibabaService->baiLianIndexFileJobStatus($consultFile->bai_lian_job_id);
            if ($alibabaService->hasError()) {
                $consultFile->update([
                    'index_status' => BailianFileConst::index_status_fail,
                    'index_reason' => $alibabaService->getError(),
                ]);
                $this->error($alibabaService->getError());
                return;
            }

            $Documents = $resp['Documents'];
            foreach ($Documents as $document) {
                $fileId = $document['DocId'];
                $status = $document['Status'];
                if ($status == "INSERT_ERROR" || $status == "DELETED") {
                    ConsultFile::query()->where('bai_lian_file_id', $fileId)->update([
                        'index_status' => BailianFileConst::index_status_fail,
                        'index_reason' => $document['Message'] ?? null,
                    ]);
                } elseif ($status == "FINISH") {
                    ConsultFile::query()->where('bai_lian_file_id', $fileId)->update([
                        'index_status' => BailianFileConst::index_status_finish,
                    ]);
                }
            }
        } else {
            $this->info("没有需要查询索引状态的文件");
        }
    }

    public function handleIndexConsultFiles()
    {
        $alibabaService = new AlibabaService();
        $consultFile    = ConsultFile::query()->where(function ($query) {
            $query->where('upload_status', BailianFileConst::upload_status_parse_success)
                ->Where('index_status', BailianFileConst::index_status_created);
        })->first();
        if ($consultFile) {
            $this->info("index 文件id:{$consultFile->id}");
            $resp = $alibabaService->baiLianIndexAddFileJob($consultFile->bai_lian_file_id);
            if ($alibabaService->hasError()) {
                $consultFile->update([
                    'index_status' => BailianFileConst::index_status_fail,
                    'index_reason' => $alibabaService->getError(),
                ]);
                $this->error($alibabaService->getError());
                return;
            }
            $jobId = $resp['Id'];
            $consultFile->update([
                'index_status'    => BailianFileConst::index_status_running,
                'bai_lian_job_id' => $jobId,
            ]);
        } else {
            $this->info("没有需要索引的文件");
        }
    }

    public function handleUploadParseConsultFiles()
    {
        $alibabaService = new AlibabaService();
        $consultFile    = ConsultFile::query()->where('upload_status', BailianFileConst::upload_status_parsing)->first();
        if ($consultFile) {
            $resp = $alibabaService->baiLianDescribeFile($consultFile->bai_lian_file_id);
            if ($alibabaService->hasError()) {
                $consultFile->update([
                    'upload_status' => BailianFileConst::upload_status_parse_fail,
                    'upload_reason' => $alibabaService->getError(),
                ]);
                $this->error($alibabaService->getError());
                return;
            }
            $status = $resp['Status'];
            if (in_array($status, ["INIT", "PARSING"])) {
                $this->info("文件id:{$consultFile->id} 正在解析");
            } else if ($status == "PARSE_SUCCESS") {
                $this->info("文件id:{$consultFile->id} 解析成功");
                $consultFile->update([
                    'upload_status' => BailianFileConst::upload_status_parse_success,
                ]);
            } else if ($status == "PARSE_FAILED") {
                $this->info("文件id:{$consultFile->id} 解析失败");
                $consultFile->update([
                    'upload_status' => BailianFileConst::upload_status_parse_fail,
                ]);
            }
        } else {
            $this->info("没有在解析的文件");
        }
    }

    public function handleUploadConsultFiles()
    {
        $alibabaService = new AlibabaService();
        $consultFile    = ConsultFile::query()->where('upload_status', BailianFileConst::upload_status_created)->first();
        if ($consultFile) {
            $this->info("upload 文件id:{$consultFile->id}");
            $attachment = Attachment::query()->where('id', $consultFile->file_id)->first();
            if ($attachment) {
                $filename   = $attachment->filename;
                $filesize   = $attachment->size;
                $fileMd5    = $attachment->md5;
                $uploadResp = $alibabaService->baiLianApplyFileUploadLease($filename, $filesize, $fileMd5, env('BAILIAN_CATEGORY_ID'));
                if ($alibabaService->hasError()) {
                    $consultFile->update([
                        'upload_status' => BailianFileConst::upload_status_fail,
                        'upload_reason' => $alibabaService->getError(),
                    ]);
                    $this->error($alibabaService->getError());
                    return;
                }
                $fileUploadLeaseId = $uploadResp['FileUploadLeaseId'];
                $url               = $uploadResp['Param']['Url'];
                $fullPath          = $attachment->full_path;
                $headers           = $uploadResp['Param']['Headers'];
                $this->info("upload 文件id:{$consultFile->id} url:{$url}");
                $this->uploadFile($url, $fullPath, $headers);
                $this->info("upload 文件id:{$consultFile->id} url:{$url} 上传完成");
                $addFileResp = $alibabaService->baiLianAddFile($fileUploadLeaseId, 'DASHSCOPE_DOCMIND', env('BAILIAN_CATEGORY_ID'), env('BAILIAN_WORKSPACE_ID'));
                if ($alibabaService->hasError()) {
                    $consultFile->update([
                        'upload_status' => BailianFileConst::upload_status_fail,
                        'upload_reason' => $alibabaService->getError(),
                    ]);
                    $this->error($alibabaService->getError());
                    return;
                }
                $baiLianFileId = $addFileResp['FileId'];
                $consultFile->update([
                    'bai_lian_file_id'  => $baiLianFileId,
                    'bai_lian_lease_id' => $fileUploadLeaseId,
                    'upload_status'     => BailianFileConst::upload_status_parsing,
                ]);
            }
        } else {
            $this->info("没有需要上传的文件");
        }
    }

    public function uploadFile($url, $fileUrl, $headers)
    {
        $client     = new Client();
        $fileStream = fopen($fileUrl, 'r');
        $resp       = $client->request('PUT', $url, [
                'headers' => $headers,
                'body'    => new Stream($fileStream)
            ]
        );
        $statusCode = $resp->getStatusCode();
        $this->info("上传结果：" . $statusCode);
    }
}
