<?php

namespace App\Http\Controllers\Common;

use Mews\Captcha\Facades\Captcha;

class CaptchaController
{
    public function getCaptcha()
    {
//        dd(sys_captcha_api_check('mmfa', 'eyJpdiI6IjdndnVIMlEvMmJJV2tyOXdMeEN5amc9PSIsInZhbHVlIjoiWFF5RmlDdERPcW5oanplYzNMQ1dhT3JaaytGZFBJZUNCM1FaQ05YenJyOXBkNEdTSEpZNWFpaTU0ZDRtU0QvMTgrUWZNaEhzUjJacnhQN0tncUZEVWw3azJyK3V6MWRhNkw2dXlTZXpRdjA9IiwibWFjIjoiODNhNDlhMjgxOTdhMzFlY2NhN2QyNGM1MzI4MGExNDM5MzQ3Nzc5ODAyNjBiYjJjYzdjNTE3MWQ0NTYxY2UyYSIsInRhZyI6IiJ9', 'flat'));

        return success(
            app('captcha')->create('flat', true)
        );
    }
}
