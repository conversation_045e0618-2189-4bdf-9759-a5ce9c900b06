<?php

namespace App\Console\Commands;

use App\Http\Consts\RegionConst;
use App\Http\Consts\RoleConst;
use App\Models\Region;
use App\Models\Role;
use App\Models\User;
use Illuminate\Console\Command;

class SetPreAdmin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:set-pre-admin';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 人武部设置为 区的管理员
        $managerDepartments = Region::query()->where('is_manager', 1)->get();
        foreach ($managerDepartments as $managerDepartment) {
            $district = $managerDepartment->parent;
            $this->info($managerDepartment->name . " " . $district->name);
            $users = User::query()->where('department_code', $managerDepartment->code)->get();
            $this->info(count($users));
            foreach ($users as $user) {
                if (!$user->hasRole(RoleConst::district_admin)) {
                    $role = Role::query()->where('slug', RoleConst::district_admin)->first();
                    $user->roles()->attach($role->id);
                }
                if (!$district->managers()->where('users.id', $user->id)->exists()) {
                    $district->managers()->attach($user->id, [
                        'tree_code' => $district->tree_code,
                    ]);
                }
            }
        }

        // 街道 学校 派出所 医院 部门
        $streets = Region::query()->where('level', RegionConst::levelStreet)->get();
        foreach ($streets as $street) {
            $users = User::query()->where('department_code', $street->code)->get();
            $this->info($street->name);
            $this->info(count($users));
            foreach ($users as $user) {
                if (!$user->hasRole(RoleConst::street_admin)) {
                    $role = Role::query()->where('slug', RoleConst::street_admin)->first();
                    $user->roles()->attach($role->id);
                }
                if (!$street->managers()->where('users.id', $user->id)->exists()) {
                    $street->managers()->attach($user->id, [
                        'tree_code' => $street->tree_code,
                    ]);
                }
            }
        }

        $schools = Region::query()->where('level', RegionConst::levelSchool)->get();
        foreach ($schools as $school) {
            $users = User::query()->where('department_code', $school->code)->get();
            $this->info($school->name);
            $this->info(count($users));
            foreach ($users as $user) {
                if (!$user->hasRole(RoleConst::school_admin)) {
                    $role = Role::query()->where('slug', RoleConst::school_admin)->first();
                    $user->roles()->attach($role->id);
                }
                if (!$school->managers()->where('users.id', $user->id)->exists()) {
                    $school->managers()->attach($user->id, [
                        'tree_code' => $school->tree_code,
                    ]);
                }
            }
        }

        $polices = Region::query()->where('level', RegionConst::levelPolice)->get();
        foreach ($polices as $police) {
            $users = User::query()->where('department_code', $police->code)->get();
            $this->info($police->name);
            $this->info(count($users));
            foreach ($users as $user) {
                if (!$user->hasRole(RoleConst::police_admin)) {
                    $role = Role::query()->where('slug', RoleConst::police_admin)->first();
                    $user->roles()->attach($role->id);
                }
                if (!$police->managers()->where('users.id', $user->id)->exists()) {
                    $police->managers()->attach($user->id, [
                        'tree_code' => $police->tree_code,
                    ]);
                }
            }
        }

        $hospitals = Region::query()->where('level', RegionConst::levelHospital)->get();
        foreach ($hospitals as $hospital) {
            $users = User::query()->where('department_code', $hospital->code)->get();
            $this->info($hospital->name);
            $this->info(count($users));
            foreach ($users as $user) {
                if (!$user->hasRole(RoleConst::hospital_admin)) {
                    $role = Role::query()->where('slug', RoleConst::hospital_admin)->first();
                    $user->roles()->attach($role->id);
                }
                if (!$hospital->managers()->where('users.id', $user->id)->exists()) {
                    $hospital->managers()->attach($user->id, [
                        'tree_code' => $hospital->tree_code,
                    ]);
                }
            }
        }

        $departments = Region::query()->where('level', RegionConst::levelDepartment)->get();
        foreach ($departments as $department) {
            $users = User::query()->where('department_code', $department->code)->get();
            $this->info($department->name);
            $this->info(count($users));
            foreach ($users as $user) {
                if (!$user->hasRole(RoleConst::department_admin)) {
                    $role = Role::query()->where('slug', RoleConst::department_admin)->first();
                    $user->roles()->attach($role->id);
                }
                if (!$department->managers()->where('users.id', $user->id)->exists()) {
                    $department->managers()->attach($user->id, [
                        'tree_code' => $department->tree_code,
                    ]);
                }
            }
        }
    }
}
