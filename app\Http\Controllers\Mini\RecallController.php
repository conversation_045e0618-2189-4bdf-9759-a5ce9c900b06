<?php

namespace App\Http\Controllers\Mini;

use App\Http\Services\Mini\RecallService;

class RecallController
{
    public function recallTask()
    {
        $params  = request()->all();
        $service = new RecallService();
        $resp    = $service->recallTask($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function recallTaskDetail($id)
    {
        $params  = request()->all();
        $service = new RecallService();
        $resp    = $service->recallTaskDetail($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function recallTaskSoldier()
    {
        $params  = request()->all();
        $service = new RecallService();
        $resp    = $service->recallTaskSoldier($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function recallTaskSoldierDetail($id)
    {
        $params  = request()->all();
        $service = new RecallService();
        $resp    = $service->recallTaskSoldierDetail($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }


    public function recallTaskSoldierStageFinish($id)
    {
        $params  = request()->all();
        $service = new RecallService();
        $resp    = $service->recallTaskSoldierStageFinish($id,$params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
    public function faceGetUserIdKey()
    {
        $params  = request()->all();
        $service = new RecallService();
        $resp    = $service->faceGetUserIdKey($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function faceGetUserFaceResult()
    {
        $params  = request()->all();
        $service = new RecallService();
        $resp    = $service->faceGetUserFaceResult($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function faceGetUserInfo()
    {
        $params  = request()->all();
        $service = new RecallService();
        $resp    = $service->faceGetUserInfo($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
