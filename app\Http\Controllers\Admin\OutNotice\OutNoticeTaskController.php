<?php

namespace App\Http\Controllers\Admin\OutNotice;

use App\Http\Services\Admin\OutNotice\OutNoticeTaskService;
use Illuminate\Http\Request;

class OutNoticeTaskController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new OutNoticeTaskService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new OutNoticeTaskService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new OutNoticeTaskService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new OutNoticeTaskService();
        $resp    = $service->update($params, $id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $params  = $request->all();
        $service = new OutNoticeTaskService();
        $resp    = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function start($id)
    {
        $service = new OutNoticeTaskService();
        $resp    = $service->start($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function stop($id)
    {
        $service = new OutNoticeTaskService();
        $resp    = $service->stop($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
