<?php

namespace App\Http\Controllers\Admin\OutNotice;

use App\Http\Services\Admin\OutNotice\OutNoticeTaskCallbackService;
use Illuminate\Http\Request;

class OutNoticeTaskCallbackController
{
    public function vmsCallFinishedCallback()
    {
        $params  = request()->all();
        $service = new OutNoticeTaskCallbackService();
        $resp    = $service->vmsCallFinishedCallback($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return response()->json([
            "code" => 0,
            "msg"  => "成功"
        ]);
    }

    public function vmsCallVoiceFinishedCallback()
    {
        $params  = request()->all();
        $service = new OutNoticeTaskCallbackService();
        $resp    = $service->vmsCallVoiceFinishedCallback($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return response()->json([
            "code" => 0,
            "msg"  => "成功"
        ]);
    }

    public function ccsCallFinishedCallback()
    {
        $params  = request()->all();
        $service = new OutNoticeTaskCallbackService();
        $resp    = $service->ccsCallFinishedCallback($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return response()->json([
            "code" => 0,
            "msg"  => "成功"
        ]);
    }
}
