<?php

namespace App\Http\Controllers\Mini;

use App\Http\Services\Mini\WorkTaskService;

class WorkTaskController
{
    /**
     * 我的任务
     */
    public function index()
    {
        $params  = request()->all();
        $service = new WorkTaskService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    /**
     * 任务详情
     */
    public function show($id)
    {
        $params  = request()->all();
        $service = new WorkTaskService();
        $resp    = $service->show($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    /**
     * 发布任务
     */
    public function store()
    {
        $params  = request()->all();
        $service = new WorkTaskService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    /**
     * 修改任务
     */
    public function update($id)
    {
        $params  = request()->all();
        $service = new WorkTaskService();
        $resp    = $service->update($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    /**
     * 删除任务
     */
    public function destroy($id)
    {
        $params  = request()->all();
        $service = new WorkTaskService();
        $resp    = $service->destroy($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function workTaskType()
    {
        $params  = request()->all();
        $service = new WorkTaskService();
        $resp    = $service->workTaskType($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
