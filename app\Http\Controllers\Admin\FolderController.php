<?php

namespace App\Http\Controllers\Admin;

use App\Http\Services\Admin\FolderService;
use Illuminate\Http\Request;

class FolderController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new FolderService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new FolderService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new FolderService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new FolderService();
        $resp    = $service->update($params, $id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $params  = $request->all();
        $service = new FolderService();
        $resp    = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }


    public function files()
    {
        $params  = request()->all();
        $service = new FolderService();
        $resp    = $service->files($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function deleteFile(Request $request, $id)
    {
        $params  = request()->all();
        $service = new FolderService();
        $resp    = $service->deleteFile($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
