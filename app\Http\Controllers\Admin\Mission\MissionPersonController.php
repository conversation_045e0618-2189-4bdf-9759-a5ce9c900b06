<?php

namespace App\Http\Controllers\Admin\Mission;

use App\Http\Consts\MissionConst;
use App\Http\Consts\MissionPersonConst;
use App\Http\Services\Admin\Mission\MissionPersonService;
use Illuminate\Http\Request;

class MissionPersonController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new MissionPersonService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new MissionPersonService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new MissionPersonService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new MissionPersonService();
        $resp    = $service->update($params, $id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $service  = new MissionPersonService();
        $params   = $request->all();
        $response = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function markIntention(Request $request)
    {
        $service = new MissionPersonService();
        $params  = $request->all();
        $resp    = $service->markIntention($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function promoteCompareList(Request $request)
    {
        $params  = $request->all();
        $service = new MissionPersonService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function politicalExamPerson(Request $request)
    {
        $params          = $request->all();
        $service         = new MissionPersonService();
        $params['scene'] = MissionConst::missionPersonListSceneMissionPoliticalExamPerson;
        $resp            = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function preStorePerson(Request $request)
    {
        $params          = $request->all();
        $service         = new MissionPersonService();
        $params['scene'] = MissionConst::missionPersonListSceneMissionPreStorePerson;
        $resp            = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function educationPerson(Request $request)
    {
        $params          = $request->all();
        $service         = new MissionPersonService();
        $params['scene'] = MissionConst::missionPersonListSceneMissionEducationPerson;
        $resp            = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
