<?php

namespace App\Jobs;

use App\Http\Consts\TaskConst;
use App\Http\Services\Admin\Task\Check\MissionInitCheckService;
use App\Http\Services\Admin\Task\Import\MissionInitImportService;
use App\Http\Services\Admin\Task\Import\MissionManyPersonListImportService;
use App\Http\Services\Admin\Task\Import\MissionPromoteComparePersonSignImportService;
use App\Http\Services\Admin\Task\Import\MissionPhysicalCheckResultImportService;
use App\Http\Services\Admin\Task\Import\MissionPhysicalRecheckResultImportService;
use App\Http\Services\Admin\Task\Import\MissionPhysicalSpotCheckResultImportService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TaskImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $task;

    /**
     * Create a new job instance.
     */
    public function __construct($task)
    {
        $this->task = $task;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $task = $this->task;

        print_r($task->type);

        switch ($task->type) {
            case TaskConst::taskTypeImportMissionInit:
                $service = new MissionInitImportService();
                $service->handle($task);
                break;

            case TaskConst::taskTypeImportMissionPromoteComparePersonSignExcel:
                $service = new MissionPromoteComparePersonSignImportService();
                $service->handle($task);
                break;


            case TaskConst::taskTypeMissionImportMissionIntranetPoliticalPersonList:
            case TaskConst::taskTypeMissionImportMissionIntranetEducationPersonList:
            case TaskConst::taskTypeMissionImportMissionIntranetPreStorePersonList:
            case TaskConst::taskTypeMissionImportMissionIntranetGoPersonStatisticsList:
                $service = new MissionManyPersonListImportService();
                $service->handle($task);
                break;

            case TaskConst::taskTypeImportMissionPhysicalCheckResult:
                $service = new MissionPhysicalCheckResultImportService();
                $service->handle($task);
                break;

            case TaskConst::taskTypeImportMissionPhysicalRecheckResult:
                $service = new MissionPhysicalRecheckResultImportService();
                $service->handle($task);
                break;

            case TaskConst::taskTypeImportMissionPhysicalSpotCheckResult:
                $service = new MissionPhysicalSpotCheckResultImportService();
                $service->handle($task);
                break;
        }
    }
}
