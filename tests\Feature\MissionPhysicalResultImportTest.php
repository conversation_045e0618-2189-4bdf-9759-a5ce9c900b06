<?php

namespace Tests\Feature;

use App\Http\Consts\ImportConst;
use App\Http\Consts\MissionPersonConst;
use App\Http\Consts\TaskConst;
use App\Http\Services\Admin\Task\Check\MissionPhysicalCheckResultCheckService;
use App\Http\Services\Admin\Task\Import\MissionPhysicalCheckResultImportService;
use App\Http\Services\Admin\Task\Save\MissionPhysicalCheckResultSavingService;
use App\Models\ImportItem;
use App\Models\Mission;
use App\Models\MissionPerson;
use App\Models\Task;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MissionPhysicalResultImportTest extends TestCase
{
    use RefreshDatabase;

    public function test_physical_check_result_constants_exist()
    {
        $this->assertEquals('mission_physical_check_result', ImportConst::importItemTypeMissionPhysicalCheckResult);
        $this->assertEquals('mission_physical_recheck_result', ImportConst::importItemTypeMissionPhysicalRecheckResult);
        $this->assertEquals('mission_physical_spot_check_result', ImportConst::importItemTypeMissionPhysicalSpotCheckResult);
    }

    public function test_task_constants_exist()
    {
        $this->assertEquals('import_mission_physical_check_result', TaskConst::taskTypeImportMissionPhysicalCheckResult);
        $this->assertEquals('import_mission_physical_recheck_result', TaskConst::taskTypeImportMissionPhysicalRecheckResult);
        $this->assertEquals('import_mission_physical_spot_check_result', TaskConst::taskTypeImportMissionPhysicalSpotCheckResult);
    }

    public function test_physical_check_result_validation()
    {
        // 创建测试任务
        $task = Task::factory()->create([
            'type' => TaskConst::taskTypeImportMissionPhysicalCheckResult,
            'params' => ['mission_id' => 1]
        ]);

        // 创建测试导入项
        $importItem = ImportItem::create([
            'task_id' => $task->id,
            'type' => ImportConst::importItemTypeMissionPhysicalCheckResult,
            'data' => json_encode([
                'name' => '张三',
                'id_card' => '110101199001011234',
                'result' => '合格'
            ]),
            'checked' => ImportConst::importItemCheckUncheck,
            'imported' => ImportConst::importItemImportedToDo,
        ]);

        // 测试检查服务
        $checkService = new MissionPhysicalCheckResultCheckService();
        $checkService->handle($task);

        // 验证检查结果
        $importItem->refresh();
        $this->assertEquals(ImportConst::importItemCheckCheckAccepted, $importItem->checked);
    }

    public function test_physical_check_result_validation_fails_for_invalid_data()
    {
        // 创建测试任务
        $task = Task::factory()->create([
            'type' => TaskConst::taskTypeImportMissionPhysicalCheckResult,
            'params' => ['mission_id' => 1]
        ]);

        // 创建测试导入项 - 缺少姓名
        $importItem = ImportItem::create([
            'task_id' => $task->id,
            'type' => ImportConst::importItemTypeMissionPhysicalCheckResult,
            'data' => json_encode([
                'name' => '',
                'id_card' => '110101199001011234',
                'result' => '合格'
            ]),
            'checked' => ImportConst::importItemCheckUncheck,
            'imported' => ImportConst::importItemImportedToDo,
        ]);

        // 测试检查服务
        $checkService = new MissionPhysicalCheckResultCheckService();
        $checkService->handle($task);

        // 验证检查结果
        $importItem->refresh();
        $this->assertEquals(ImportConst::importItemCheckError, $importItem->checked);
        $this->assertEquals('姓名不能为空', $importItem->message);
    }
}
