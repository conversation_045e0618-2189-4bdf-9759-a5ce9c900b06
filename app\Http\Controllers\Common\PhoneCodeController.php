<?php

namespace App\Http\Controllers\Common;

use App\Http\Consts\PhoneCodeScene;
use App\Http\Services\Common\PhoneCodeService;
use App\Models\User;
use App\Rules\ContactNum;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\In;

class PhoneCodeController
{

    /**
     * 获取短信验证码
     */
    public function send()
    {
        $form = request()->input();

        $va = Validator::make($form, [
            'phone'        => [
                'required',
                new ContactNum('手机号')
            ],
            'scene'        => [
                'required',
                new In(array_keys(PhoneCodeScene::sceneMap()))
            ],
            'captcha_key'  => 'required_if:scene,' . implode(',', [PhoneCodeScene::register, PhoneCodeScene::reset_password, PhoneCodeScene::update_password]),
            'captcha_code' => 'required_if:scene,' . implode(',', [PhoneCodeScene::register, PhoneCodeScene::reset_password, PhoneCodeScene::update_password]),
        ], [
            'phone.required'        => '请输入手机号',
            'scene.required'        => '缺少场景值',
            'scene.in'              => '场景值错误',
            'captcha_key.required'  => '请输入验证码key',
            'captcha_code.required' => '请输入验证码',
        ]);

        if ($va->fails()) {
            return error($va->errors()->first());
        }
        $phone = $form['phone'] ?? '';
        $scene = $form['scene'] ?? '';

        $captcha_key  = $form['captcha_key'] ?? null;
        $captcha_code = $form['captcha_code'] ?? null;
        if ($captcha_key && $captcha_code) {
            if (!sys_captcha_api_check($captcha_code, $captcha_key, 'flat')) {
                return error('图形验证码错误');
            }
        }

        // 如果场景值是 专家找回密码
        if ($scene == PhoneCodeScene::reset_password) {
            // 判断手机号是否存在
            $user = User::query()->where('phone', $phone)->first();
            if (!$user) {
                return error('该手机号未注册');
            }
        } else if ($scene == PhoneCodeScene::register) {
            $user = User::query()->where('phone', $phone)->first();
            if ($user) {
                return error('该手机号已注册');
            }
        }


        $phoneCodeService = new PhoneCodeService();
        $phoneCodeService->setParams($phone, $scene);
        $resp = $phoneCodeService->send();
        if ($phoneCodeService->hasError()) {
            return error($phoneCodeService->getError());
        }
        return success();
    }

}
