<?php

namespace App\Console\Commands;

use App\Models\ImportItem;
use App\Models\MissionPerson;
use App\Models\Task;
use App\Http\Consts\ImportConst;
use App\Http\Consts\MissionPersonConst;
use Illuminate\Console\Command;

class DebugPhysicalImport extends Command
{
    protected $signature = 'debug:physical-import {--task-id= : 指定任务ID}';
    protected $description = '调试体检结果导入问题';

    public function handle()
    {
        $taskId = $this->option('task-id');
        
        if ($taskId) {
            $this->debugSpecificTask($taskId);
        } else {
            $this->debugRecentImports();
        }
    }

    private function debugSpecificTask($taskId)
    {
        $this->info("=== 调试任务 ID: {$taskId} ===");
        
        $task = Task::find($taskId);
        if (!$task) {
            $this->error("任务不存在");
            return;
        }
        
        $this->info("任务类型: {$task->type}");
        $this->info("任务参数: " . json_encode($task->params, JSON_UNESCAPED_UNICODE));
        
        $missionId = $task->params['mission_id'] ?? null;
        if (!$missionId) {
            $this->error("任务中没有mission_id");
            return;
        }
        
        // 获取该任务的导入项
        $importItems = ImportItem::query()
            ->where('task_id', $taskId)
            ->where('type', ImportConst::importItemTypeMissionPhysicalCheckResult)
            ->get();
        
        $this->info("找到 {$importItems->count()} 条导入记录");
        
        foreach ($importItems as $item) {
            $this->debugImportItem($item, $missionId);
        }
    }

    private function debugRecentImports()
    {
        $this->info("=== 最近的体检结果导入记录 ===");
        
        $recentImportItems = ImportItem::query()
            ->where('type', ImportConst::importItemTypeMissionPhysicalCheckResult)
            ->orderBy('id', 'desc')
            ->limit(5)
            ->get();

        foreach ($recentImportItems as $item) {
            $task = Task::find($item->task_id);
            $missionId = $task->params['mission_id'] ?? null;
            
            $this->debugImportItem($item, $missionId);
        }
    }

    private function debugImportItem($item, $missionId)
    {
        $this->info("--- 导入项 ID: {$item->id} ---");
        
        // 解析数据
        $data = is_string($item->data) ? json_decode($item->data, true) : $item->data;
        $this->info("原始数据: " . json_encode($data, JSON_UNESCAPED_UNICODE));
        
        $this->info("导入状态: " . $this->getImportedStatusText($item->imported));
        $this->info("检查状态: " . $this->getCheckedStatusText($item->checked));
        
        if (!isset($data['id_card'])) {
            $this->error("数据中没有身份证号");
            return;
        }
        
        $idCard = trim($data['id_card']);
        $result = trim($data['result'] ?? '');
        $remark = trim($data['remark'] ?? '');
        
        $this->info("身份证号: {$idCard}");
        $this->info("结果: {$result}");
        $this->info("备注: {$remark}");
        
        // 转换结果值
        $physicalCheck = $this->convertResultToPhysicalCheck($result);
        $physicalResult = $this->convertResultToPhysicalResult($result);
        
        $this->info("转换后的体检状态: {$physicalCheck}");
        $this->info("转换后的体检结果: {$physicalResult}");
        
        if (!$missionId) {
            $this->error("没有mission_id");
            return;
        }
        
        // 查找人员记录
        $persons = MissionPerson::query()
            ->where('mission_id', $missionId)
            ->where('id_card', $idCard)
            ->get();
        
        $this->info("找到 {$persons->count()} 条人员记录");
        
        if ($persons->count() > 0) {
            foreach ($persons as $person) {
                $this->info("人员: {$person->name} (ID: {$person->id})");
                $this->info("当前体检状态: {$person->physical_check}");
                $this->info("当前体检结果: {$person->physical_result}");
                $this->info("体检备注: {$person->physical_check_remark}");
                $this->info("体检时间: {$person->physical_check_at}");
            }
        } else {
            $this->error("未找到对应人员记录");
            
            // 查找相似的身份证号
            $similarPersons = MissionPerson::query()
                ->where('mission_id', $missionId)
                ->where('id_card', 'like', '%' . substr($idCard, -6) . '%')
                ->get(['id', 'name', 'id_card']);
            
            if ($similarPersons->count() > 0) {
                $this->warn("找到相似身份证号的人员:");
                foreach ($similarPersons as $similar) {
                    $this->line("  - {$similar->name} ({$similar->id_card})");
                }
            }
            
            // 检查任务中总共有多少人员
            $totalPersons = MissionPerson::query()->where('mission_id', $missionId)->count();
            $this->info("任务中总人员数: {$totalPersons}");
        }
        
        $this->line("");
    }

    private function convertResultToPhysicalCheck($result)
    {
        switch ($result) {
            case '合格':
            case '1':
                return MissionPersonConst::personPhysicalCheckYes;
            case '不合格':
            case '2':
                return MissionPersonConst::personPhysicalCheckNo;
            default:
                return MissionPersonConst::personPhysicalCheckUnknown;
        }
    }

    private function convertResultToPhysicalResult($result)
    {
        switch ($result) {
            case '合格':
            case '1':
                return MissionPersonConst::personPhysicalResultYes;
            case '不合格':
            case '2':
                return MissionPersonConst::personPhysicalResultNo;
            default:
                return MissionPersonConst::personPhysicalResultUnknown;
        }
    }

    private function getImportedStatusText($status)
    {
        switch ($status) {
            case ImportConst::importItemImportedToDo:
                return '待导入';
            case ImportConst::importItemImportedError:
                return '导入失败';
            case ImportConst::importItemImportedDone:
                return '已导入';
            default:
                return "未知({$status})";
        }
    }

    private function getCheckedStatusText($status)
    {
        switch ($status) {
            case ImportConst::importItemCheckUncheck:
                return '未检查';
            case ImportConst::importItemCheckError:
                return '检查失败';
            case ImportConst::importItemCheckCheckAccepted:
                return '检查通过';
            default:
                return "未知({$status})";
        }
    }
}
