<?php

namespace App\Http\Controllers\Mini;

use App\Http\Services\Mini\SurveyService;

class SurveyController
{
    public function list()
    {
        $params  = request()->all();
        $service = new SurveyService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function detail($id)
    {
        $params  = request()->all();
        $service = new SurveyService();
        $resp    = $service->detail($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function answer($id)
    {
        $params  = request()->all();
        $service = new SurveyService();
        $resp    = $service->answer($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
