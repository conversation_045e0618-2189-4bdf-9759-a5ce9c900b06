<?php

namespace App\Http\Utils;

use App\Http\Consts\PermissionSlugConst;
use App\Models\Report;

interface  ScopeQuery
{
    /**
     * 举报
     */
    public static function miniGetReportQuery($user, $params);

    /**
     * 工作任务
     */
    public static function miniGetWorkTaskQuery($user, $params);

    public static function miniGetWorkTaskReportQuery($user, $params);

    /**
     * 通知
     */
    public static function miniGetInformQuery($user, $params);

    /**
     * 调查
     */
    public static function miniGetSurveyQuery($user, $params);


    /**
     * 特殊时期征召
     */
    public static function miniGetRecallTaskSoldierQuery($user, $params);

    public static function miniGetRecallTaskQuery($user, $params);

    /**
     * 外呼任务
     */
    public static function miniGetOutNoticeTaskQuery($user, $params);

    public static function miniGetNotificationQuery($user, $params);

    /**
     * 任务人员
     */

    public static function miniGetMissionPersonQuery($user, $params);

    // =============================================================================

    /**
     * 工作任务
     */
    public static function adminGetWorkTaskQuery($user, $params);

    public static function adminGetWorkTaskReportQuery($user, $params);

    public static function adminGetInformQuery($user, $params);

    public static function adminGetReportQuery($user, $params);


    /**
     * 特殊时期征召任务成员
     */
    public static function adminGetRecallTaskSoldierQuery($user, $params);

    /**
     * 特殊时期征召任务
     */
    public static function adminGetRecallTaskQuery($user, $params);

    /**
     * 用户
     */
    public static function adminGetUserQuery($user, $params);

    /**
     * 授权码
     */
    public static function adminGetInvitationCodeQuery($user, $params);

    /**
     * 文章数量
     */
    public static function adminGetArticleCountQuery($user, $params);

    /**
     * 调查表
     */
    public static function adminGetSurveyQuery($user, $params);

    public static function adminGetSurveyFormQuery($user, $params);


    public static function adminGetNotificationQuery($user, $params);

    public static function adminGetStatisticsUserPointQueryItems($user, $params);

    public static function adminGetStatisticsUserCourseQuery($user, $params);

    public static function adminGetStatisticsDepartmentCourseQueryItems($user, $params);

    public static function adminGetStatisticsCourseQuery($user, $params);

    public static function adminGetStatisticsUserPaperQueryItems($user, $params);

    public static function adminGetStatisticsDepartmentPaperQueryItems($user, $params);

    public static function adminGetMissionPersonQuery($user, $params);

    public static function adminGetRegionWithCodeQuery($user, $params);

    public static function adminGetRegionWithTreeCodeQuery($user, $params);

    public static function adminGetMissionRegionSettingQuery($user, $params);

    public static function adminGetMissionSignPersonQuery($user, $params);

        public static function adminGetMissionPhysicalExaminationQuery($user, $params);

}
