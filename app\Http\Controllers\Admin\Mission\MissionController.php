<?php

namespace App\Http\Controllers\Admin\Mission;

use App\Http\Services\Admin\Mission\MissionService;
use Illuminate\Http\Request;

class MissionController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new MissionService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new MissionService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new MissionService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new MissionService();
        $resp    = $service->update($params, $id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $service = new MissionService();
        $params = $request->all();
        $response = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }
}
