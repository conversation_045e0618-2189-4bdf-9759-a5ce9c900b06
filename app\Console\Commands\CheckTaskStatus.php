<?php

namespace App\Console\Commands;

use App\Models\Task;
use App\Http\Consts\TaskConst;
use Illuminate\Console\Command;

class CheckTaskStatus extends Command
{
    protected $signature = 'debug:task-status {task-id}';
    protected $description = '检查任务执行状态';

    public function handle()
    {
        $taskId = $this->argument('task-id');
        
        $task = Task::find($taskId);
        if (!$task) {
            $this->error("任务不存在");
            return;
        }
        
        $this->info("=== 任务状态 ===");
        $this->info("任务ID: {$task->id}");
        $this->info("任务类型: {$task->type}");
        $this->info("任务状态: {$task->status}");
        $this->info("创建时间: {$task->created_at}");
        $this->info("开始时间: {$task->start_at}");
        $this->info("结束时间: {$task->end_at}");
        $this->info("错误信息: {$task->error}");
        $this->info("参数: " . json_encode($task->params, JSON_UNESCAPED_UNICODE));
        
        // 检查任务是否是体检结果导入类型
        $physicalImportTypes = [
            TaskConst::taskTypeImportMissionPhysicalCheckResult,
            TaskConst::taskTypeImportMissionPhysicalRecheckResult,
            TaskConst::taskTypeImportMissionPhysicalSpotCheckResult,
        ];
        
        if (in_array($task->type, $physicalImportTypes)) {
            $this->info("\n=== 这是体检结果导入任务 ===");
            
            // 检查任务执行流程
            $this->checkTaskFlow($task);
        }
    }
    
    private function checkTaskFlow($task)
    {
        $this->info("检查任务执行流程...");
        
        // 1. 检查是否有解析阶段 (TaskJob)
        if ($task->status == 'parsed' || $task->status == 'success') {
            $this->info("✓ 解析阶段已完成");
        } else {
            $this->warn("✗ 解析阶段未完成，当前状态: {$task->status}");
        }
        
        // 2. 检查是否有检查阶段 (TaskCheckJob)
        $importItems = \App\Models\ImportItem::where('task_id', $task->id)->get();
        $this->info("导入项总数: {$importItems->count()}");
        
        $checkedCount = $importItems->where('checked', \App\Http\Consts\ImportConst::importItemCheckCheckAccepted)->count();
        $errorCount = $importItems->where('checked', \App\Http\Consts\ImportConst::importItemCheckError)->count();
        $uncheckCount = $importItems->where('checked', \App\Http\Consts\ImportConst::importItemCheckUncheck)->count();
        
        $this->info("检查通过: {$checkedCount}");
        $this->info("检查失败: {$errorCount}");
        $this->info("未检查: {$uncheckCount}");
        
        // 3. 检查是否有导入阶段 (TaskImportJob)
        $importedCount = $importItems->where('imported', \App\Http\Consts\ImportConst::importItemImportedDone)->count();
        $importErrorCount = $importItems->where('imported', \App\Http\Consts\ImportConst::importItemImportedError)->count();
        $toImportCount = $importItems->where('imported', \App\Http\Consts\ImportConst::importItemImportedToDo)->count();
        
        $this->info("已导入: {$importedCount}");
        $this->info("导入失败: {$importErrorCount}");
        $this->info("待导入: {$toImportCount}");
        
        // 4. 显示一些示例数据
        if ($importItems->count() > 0) {
            $this->info("\n=== 示例导入项 ===");
            $sampleItem = $importItems->first();
            $data = is_string($sampleItem->data) ? json_decode($sampleItem->data, true) : $sampleItem->data;
            $this->info("示例数据: " . json_encode($data, JSON_UNESCAPED_UNICODE));
            $this->info("检查状态: {$sampleItem->checked}");
            $this->info("导入状态: {$sampleItem->imported}");
            if ($sampleItem->message) {
                $this->info("错误信息: {$sampleItem->message}");
            }
        }
    }
}
