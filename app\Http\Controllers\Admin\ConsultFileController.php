<?php

namespace App\Http\Controllers\Admin;

use App\Http\Services\Admin\ConsultFileService;
use Illuminate\Http\Request;

class ConsultFileController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new ConsultFileService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new ConsultFileService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new ConsultFileService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }


    public function destroy(Request $request, $id)
    {
        $params  = $request->all();
        $service = new ConsultFileService();
        $resp    = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
