<?php

namespace App\Http\Controllers\Mini;

use App\Http\Services\Mini\HomeService;

class HomeController
{
    public function index()
    {
        $service = new HomeService();
        $resp    = $service->index();
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function articles()
    {
        $params  = request()->all();
        $service = new HomeService();
        $resp    = $service->articles($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function article($id)
    {
        $service = new HomeService();
        $resp    = $service->article($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function page($id)
    {
        $service = new HomeService();
        $resp    = $service->page($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function categories()
    {
        $params  = request()->all();
        $service = new HomeService();
        $resp    = $service->categories($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function category($id)
    {
        $service = new HomeService();
        $resp    = $service->category($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function config()
    {
        $service = new HomeService();
        $resp    = $service->config();
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function businessTypes()
    {
        $service = new HomeService();
        $resp    = $service->businessTypes();
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function collect()
    {
        $params  = request()->all();
        $service = new HomeService();
        $resp    = $service->collect($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function myCollect()
    {
        $params  = request()->all();
        $service = new HomeService();
        $resp    = $service->myCollect($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    // stage
    public function stage()
    {
        $params  = request()->all();
        $service = new HomeService();
        $resp    = $service->stage($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}

