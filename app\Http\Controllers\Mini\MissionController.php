<?php

namespace App\Http\Controllers\Mini;

use App\Http\Services\Mini\MissionService;
use Illuminate\Http\Request;

class MissionController
{
    /**
     * 获取任务列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $params = $request->all();
        $service = new MissionService();
        $resp = $service->index($params);
        
        if ($service->hasError()) {
            return error($service->getError());
        }
        
        return success($resp);
    }

    /**
     * 获取任务详情
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $params = $request->all();
        $service = new MissionService();
        $resp = $service->show($id, $params);
        
        if ($service->hasError()) {
            return error($service->getError());
        }
        
        return success($resp);
    }
} 