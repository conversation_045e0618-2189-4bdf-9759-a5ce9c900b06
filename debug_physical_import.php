<?php

// 调试体检结果导入问题的脚本
// 使用方法: php debug_physical_import.php

require_once 'vendor/autoload.php';

use App\Models\ImportItem;
use App\Models\MissionPerson;
use App\Http\Consts\ImportConst;

// 获取最近的体检结果导入任务
$recentImportItems = ImportItem::query()
    ->where('type', ImportConst::importItemTypeMissionPhysicalCheckResult)
    ->where('imported', ImportConst::importItemImportedDone)
    ->orderBy('id', 'desc')
    ->limit(5)
    ->get();

echo "=== 最近的体检结果导入记录 ===\n";
foreach ($recentImportItems as $item) {
    $data = is_string($item->data) ? json_decode($item->data, true) : $item->data;
    
    echo "导入项ID: {$item->id}\n";
    echo "任务ID: {$item->task_id}\n";
    echo "数据: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n";
    echo "导入状态: {$item->imported}\n";
    echo "检查状态: {$item->checked}\n";
    
    // 查找对应的人员记录
    if (isset($data['id_card'])) {
        $idCard = trim($data['id_card']);
        
        // 从任务中获取mission_id
        $task = \App\Models\Task::find($item->task_id);
        $missionId = $task->params['mission_id'] ?? null;
        
        if ($missionId) {
            $person = MissionPerson::query()
                ->where('mission_id', $missionId)
                ->where('id_card', $idCard)
                ->first();
            
            if ($person) {
                echo "找到对应人员: {$person->name} (ID: {$person->id})\n";
                echo "当前体检状态: {$person->physical_check}\n";
                echo "当前体检结果: {$person->physical_result}\n";
                echo "体检备注: {$person->physical_check_remark}\n";
                echo "体检时间: {$person->physical_check_at}\n";
            } else {
                echo "未找到对应人员 (Mission ID: {$missionId}, 身份证: {$idCard})\n";
                
                // 查找相似的身份证号
                $similarPersons = MissionPerson::query()
                    ->where('mission_id', $missionId)
                    ->where('id_card', 'like', '%' . substr($idCard, -6) . '%')
                    ->get(['id', 'name', 'id_card']);
                
                if ($similarPersons->count() > 0) {
                    echo "找到相似身份证号的人员:\n";
                    foreach ($similarPersons as $similar) {
                        echo "  - {$similar->name} ({$similar->id_card})\n";
                    }
                }
            }
        } else {
            echo "任务中未找到mission_id\n";
        }
    }
    
    echo "---\n";
}

echo "\n=== 检查常量值 ===\n";
echo "personPhysicalCheckYes: " . \App\Http\Consts\MissionPersonConst::personPhysicalCheckYes . "\n";
echo "personPhysicalCheckNo: " . \App\Http\Consts\MissionPersonConst::personPhysicalCheckNo . "\n";
echo "personPhysicalResultYes: " . \App\Http\Consts\MissionPersonConst::personPhysicalResultYes . "\n";
echo "personPhysicalResultNo: " . \App\Http\Consts\MissionPersonConst::personPhysicalResultNo . "\n";
