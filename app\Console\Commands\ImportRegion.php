<?php

namespace App\Console\Commands;

use App\Http\Consts\RegionConst;
use App\Models\Region;
use Goutte\Client;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class ImportRegion extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-region';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Region::query()->delete();

        Region::query()->create([
            'code'   => 'c1101',
            'name'   => '北京市',
            'p_code' => 0,
            'level'  => RegionConst::levelCity,
            'depth'  => 0,
        ]);

        $startUrl = "https://www.stats.gov.cn/sj/tjbz/tjyqhdmhcxhfdm/2023/11/1101.html";
        $this->handleCountryPage($startUrl, 'c1101');
    }

    private function handleCountryPage(string $url, $pCode = 0)
    {
        $client  = new Client();
        $crawler = $client->request('GET', $url);
        $trs     = $crawler->filter('.countytr');
        $trs->each(function ($tr) use ($pCode) {
            $code    = "";
            $name    = "";
            $fullUrl = "";
            $tr->filter('td:nth-child(1) a')->each(function ($a) use (&$code) {
                $code = rtrim($a->text(), "0");
            });
            $tr->filter('td:nth-child(2) a')->each(function ($a) use (&$name, &$fullUrl) {
                $href    = $a->attr('href');
                $name    = $a->text();
                $fullUrl = "https://www.stats.gov.cn/sj/tjbz/tjyqhdmhcxhfdm/2023/11/" . $href;
            });

            if ($code && $name) {
                $this->info($code . " " . $name);
                $code = 'c' . $code;
                Region::query()->create([
                    'code'   => $code,
                    'name'   => $name,
                    'p_code' => $pCode,
                    'level'  => RegionConst::levelDistrict,
                    'depth'  => 1,
                ]);
            }
            if ($fullUrl) {
                $this->handleTownPage($fullUrl, $code);
            }
        });
    }


    private function handleTownPage(string $url, $pCode = 0)
    {
        $client  = new Client();
        $crawler = $client->request('GET', $url);
        $trs     = $crawler->filter('.towntr');
        $trs->each(function ($tr) use ($url, $pCode) {
            $code    = "";
            $name    = "";
            $fullUrl = "";
            $tr->filter('td:nth-child(1) a')->each(function ($a) use (&$code) {
                $code = rtrim($a->text(), "0");
            });
            $tr->filter('td:nth-child(2) a')->each(function ($a) use ($url, &$name, &$fullUrl) {
                $href = $a->attr('href');
                $name = $a->text();
//                $this->info($name);
//                print_r($href);
                // url 获取路径
                $fullUrl = dirname($url) . "/" . $href;

            });

            if ($code && $name) {
                $this->info($code . " " . $name);
                $code = 'c' . $code;
                Region::query()->create([
                    'code'   => $code,
                    'name'   => $name,
                    'p_code' => $pCode,
                    'level'  => RegionConst::levelStreet,
                    'depth'  => 2,
                ]);
            }

            if ($fullUrl) {
                $this->handleVillagePage($fullUrl, $code);
            }
        });
    }

    private function handleVillagePage(string $url, $pCode = 0)
    {
        $client  = new Client();
        $crawler = $client->request('GET', $url);
        $trs     = $crawler->filter('.villagetr');
        $trs->each(function ($tr) use ($pCode) {
            $code = "";
            $name = "";
            $tr->filter('td:nth-child(1)')->each(function ($td) use (&$code) {
                $code = rtrim($td->text(), "0");
            });
            $tr->filter('td:nth-child(3)')->each(function ($td) use (&$name) {
                $name = $td->text();
            });

            if ($code && $name) {
                $name = Str::replace('居民委员会', '', $name);
                $name = Str::replace('居委会', '', $name);
                $name = Str::replace('村委会', '村', $name);
                $code = 'c' . $code;
                Region::query()->create([
                    'code'   => $code,
                    'name'   => $name,
                    'p_code' => $pCode,
                    'level'  => RegionConst::levelVillage,
                    'depth'  => 3,
                ]);
                $this->info($code . " " . $name);
            }
        });
    }


}
