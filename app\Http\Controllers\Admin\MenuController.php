<?php

namespace App\Http\Controllers\Admin;

use App\Http\Services\Admin\MenuService;
use Illuminate\Http\Request;

class MenuController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new MenuService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new MenuService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new MenuService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new MenuService();
        $resp    = $service->update($params, $id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $service  = new MenuService();
        $params   = $request->all();
        $response = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }
}
