<?php

namespace App\Http\Controllers\Admin\Mission;

use App\Http\Services\Admin\Mission\MissionPhysicalExaminationService;
use Illuminate\Http\Request;

class MissionPhysicalExaminationController

{
    public function stationRateStore(Request $request)
    {
        $service = new MissionPhysicalExaminationService();
        $params  = $request->all();
        $resp = $service->stationRateStore($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
    public function stationRateShow(Request $request)
    {
        $service = new MissionPhysicalExaminationService();
        $params  = $request->all();
        $resp = $service->stationRateShow($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new MissionPhysicalExaminationService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new MissionPhysicalExaminationService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new MissionPhysicalExaminationService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new MissionPhysicalExaminationService();
        $resp    = $service->update($params, $id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $service  = new MissionPhysicalExaminationService();
        $params   = $request->all();
        $response = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function personList()
    {
        $service = new MissionPhysicalExaminationService();
        $params  = request()->all();
        $resp    = $service->personList($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function personDelete(Request $request)
    {
        $service = new MissionPhysicalExaminationService();
        $params  = $request->all();
        $resp    = $service->personDelete($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function submit(Request $request)
    {
        $service = new MissionPhysicalExaminationService();
        $params  = $request->all();
        $resp    = $service->submit($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function recall(Request $request)
    {
        $service = new MissionPhysicalExaminationService();
        $params  = $request->all();
        $resp    = $service->recall($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }


    public function markResult()
    {
        $service = new MissionPhysicalExaminationService();
        $params  = request()->all();
        $resp    = $service->markResult($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function markRecheckResult()
    {
        $service = new MissionPhysicalExaminationService();
        $params  = request()->all();
        $resp    = $service->markRecheckResult($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function markSpotCheckResult()
    {
        $service = new MissionPhysicalExaminationService();
        $params  = request()->all();
        $resp    = $service->markSpotCheckResult($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
