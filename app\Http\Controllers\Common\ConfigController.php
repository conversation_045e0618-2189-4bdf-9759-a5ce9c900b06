<?php

namespace App\Http\Controllers\Common;

use App\Http\Services\Common\ConfigService;

class ConfigController
{
    public function store()
    {
        $params   = request()->all();
        $service  = new ConfigService();
        $response = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function get()
    {
        $service  = new ConfigService();
        $response = $service->get();
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }
}
