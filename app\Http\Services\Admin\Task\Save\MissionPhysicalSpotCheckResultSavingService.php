<?php

namespace App\Http\Services\Admin\Task\Save;

use App\Exceptions\ErrorTrait;
use App\Http\Consts\ImportConst;
use App\Http\Traits\ExcelTraits;
use App\Models\ImportItem;

class MissionPhysicalSpotCheckResultSavingService
{
    use ErrorTrait, ExcelTraits;

    protected $task;

    public function import($params, $task)
    {
        $this->task = $task;
        $attachmentId = $params['attachment_id'] ?? '';

        $rows = $this->readExcelFormAttachmentId($attachmentId);

        $dataItems = [];

        foreach ($rows as $index => $row) {
            if ($index == 1) {
                continue; // 跳过表头行
            }
            $dataItems[] = [
                'name'    => trim($row[1] ?? ''),
                'id_card' => trim($row[2] ?? ''),
                'result'  => trim($row[3] ?? ''),
            ];
        }

        $this->handleItems($dataItems);
    }

    public function handleItems($rows)
    {
        $items = [];
        foreach ($rows as $row) {
            $item    = [
                "type"       => ImportConst::importItemTypeMissionPhysicalSpotCheckResult,
                "task_id"    => $this->task->id,
                "data"       => json_encode($row),
                "imported"   => ImportConst::importItemImportedToDo,
                "checked"    => ImportConst::importItemCheckUncheck,
                "created_at" => now(),
            ];
            $items[] = $item;
        }
        ImportItem::query()->insert($items);
    }
}
