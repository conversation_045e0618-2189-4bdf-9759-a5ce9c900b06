<?php

namespace App\Http\Controllers\Admin\Work;


use App\Http\Services\Admin\Work\WorkTaskReportService;

class WorkTaskReportController
{
    /**
     * 我的任务
     */
    public function index()
    {
        $params  = request()->all();
        $service = new WorkTaskReportService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    /**
     * 任务详情
     */
    public function show($id)
    {
        $params  = request()->all();
        $service = new WorkTaskReportService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    /**
     * 发布任务
     */
    public function store()
    {
        $params  = request()->all();
        $service = new WorkTaskReportService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    /**
     * 修改任务
     */
    public function update($id)
    {
        $params  = request()->all();
        $service = new WorkTaskReportService();
        $resp    = $service->update($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    /**
     * 删除任务
     */
    public function destroy($id)
    {
        $params  = request()->all();
        $service = new WorkTaskReportService();
        $resp    = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
