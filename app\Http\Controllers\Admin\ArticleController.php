<?php

namespace App\Http\Controllers\Admin;

use App\Http\Services\Admin\ArticleService;
use App\Models\Article;
use Illuminate\Http\Request;

class ArticleController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new ArticleService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new ArticleService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new ArticleService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new ArticleService();
        $resp    = $service->update($params, $id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $service = new ArticleService();
        $params = $request->all();
        $response = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }
}
