<?php

namespace App\Http\Consts;

class OutNoticeConst
{
    const smsTemplateStatusReviewing = 0;
    const smsTemplateStatusPassed = 1;
    const smsTemplateStatusRejected = 2;
    const smsTemplateStatusCanceled = 10;

    const taskTypeSms = 'sms';
    const taskTypeVms = 'vms';
    const taskTypeSmsVms = 'sms_vms';
    const taskTypeRobot = 'robot';

    const taskStatusCreated = 'created';
    const taskStatusRunning = 'running';
    const taskStatusStopped = 'stopped';
    const taskStatusFinished = 'finished';


    const taskReceiveTypeSms = 'sms';
    const taskReceiveTypeVms = 'vms';
    const taskReceiveTypeRobot = 'robot';

    const taskReceiveTypeMap = [
        self::taskReceiveTypeSms   => '短信',
        self::taskReceiveTypeVms   => '语音',
        self::taskReceiveTypeRobot => '机器人',
    ];

    const taskReceiveStatusCreated = 'created';
    const taskReceiveStatusRunning = 'running';
    const taskReceiveStatusFinished = 'finished';
    const taskReceiveStatusError = 'error';

    const taskReceiveStatusMap = [
        self::taskReceiveStatusCreated  => '待处理',
        self::taskReceiveStatusRunning  => '运行中',
        self::taskReceiveStatusFinished => '已完成',
        self::taskReceiveStatusError    => '失败',
    ];


    const smsRulePhone = 'phone_number2';
    const smsRuleTime = 'time';
    const smsRuleMoney = 'money';
    const smsRuleUserNick = 'user_nick';
    const smsRuleName = 'name';
    const smsRuleUnitName = 'unit_name';
    const smsRuleAddress = 'address';
    const smsRuleLicensePlateNumber = 'license_plate_number';
    const smsRuleTrackingNumber = 'tracking_number';
    const smsRulePickUpCode = 'pick_up_code';
    const smsRuleOtherNumber2 = 'other_number2';
    const smsRuleLinkParam = 'link_param';
    const smsRuleEmailAddress = 'email_address';
    const smsRuleOthers = 'others';
    const smsRuleMap = [
        self::smsRulePhone              => '电话号码',
        self::smsRuleTime               => '时间',
        self::smsRuleMoney              => '金额',
        self::smsRuleUserNick           => '用户昵称',
        self::smsRuleName               => '姓名',
        self::smsRuleUnitName           => '单位名称',
        self::smsRuleAddress            => '地址',
        self::smsRuleLicensePlateNumber => '车牌号',
        self::smsRuleTrackingNumber     => '运单号',
        self::smsRulePickUpCode         => '取件码',
        self::smsRuleOtherNumber2       => '其他号码',
        self::smsRuleLinkParam          => '链接参数',
        self::smsRuleEmailAddress       => '邮箱地址',
        self::smsRuleOthers             => '其他',
    ];

    const callbackMessageTypeVmsCallFinished = 'vms_call_finished';
    const callbackMessageTypeVmsCallVoiceFinished = 'vms_call_voice_finished';
    const callbackMessageTypeCcsCallFinished = 'ccs_call_finished';
}
