<?php

namespace App\Http\Controllers\Admin;

use App\Http\Services\Admin\Statistics\Mission\MissionGoStatisticsService;
use App\Http\Services\Admin\Statistics\Mission\MissionIntentionStatisticsService;
use App\Http\Services\Admin\Statistics\Mission\MissionPhysicalCheckStatisticsService;
use App\Http\Services\Admin\Statistics\Mission\MissionPhysicalRecheckStatisticsService;
use App\Http\Services\Admin\Statistics\Mission\MissionPhysicalSpotCheckStatisticsService;
use App\Http\Services\Admin\Statistics\Mission\MissionPhysicalTaskNumStatisticsService;
use App\Http\Services\Admin\Statistics\Mission\MissionPromoteCompareStatisticsService;
use App\Http\Services\Admin\Statistics\Mission\MissionPoliticalExamStatisticsService;
use App\Http\Services\Admin\Statistics\Mission\MissionEducationStatisticsService;
use App\Http\Services\Admin\Statistics\Mission\MissionPreStoreStatisticsService;
use App\Http\Services\Admin\Statistics\Mission\MissionSchoolPersonStatisticsService;
use App\Http\Services\Admin\Statistics\Mission\MissionSocialPersonStatisticsService;
use App\Http\Services\Admin\Statistics\Mission\MissionTaskNumStatisticsService;
use App\Http\Services\Admin\Statistics\Mission\MissionSummaryStatisticsService;
use App\Http\Services\Admin\Statistics\Mission\MissionDetailStatisticsService;
use App\Http\Services\Admin\Statistics\StatisticsService;
use App\Http\Services\Admin\Task\Refresh\MissionPromoteCompareStatisticsCacheService;


class StatisticsController
{
    public function courseStatistics()
    {
        $params            = request()->all();
        $statisticsService = new StatisticsService();
        $resp              = $statisticsService->courseStatistics($params);
        if ($statisticsService->hasError()) {
            return error($statisticsService->getError());
        }
        return success($resp);
    }


    public function userCourseStatistics()
    {
        $params            = request()->all();
        $statisticsService = new StatisticsService();
        $resp              = $statisticsService->userCourseStatistics($params);
        if ($statisticsService->hasError()) {
            return error($statisticsService->getError());
        }
        return success($resp);
    }

    public function departmentCourseStatistics()
    {
        $params            = request()->all();
        $statisticsService = new StatisticsService();
        $resp              = $statisticsService->departmentCourseStatistics($params);
        if ($statisticsService->hasError()) {
            return error($statisticsService->getError());
        }
        return success($resp);
    }

    public function userPaperRoundStatistics()
    {
        $params            = request()->all();
        $statisticsService = new StatisticsService();
        $resp              = $statisticsService->userPaperRoundStatistics($params);
        if ($statisticsService->hasError()) {
            return error($statisticsService->getError());
        }
        return success($resp);
    }

    public function departmentPaperRoundStatistics()
    {
        $params            = request()->all();
        $statisticsService = new StatisticsService();
        $resp              = $statisticsService->departmentPaperRoundStatistics($params);
        if ($statisticsService->hasError()) {
            return error($statisticsService->getError());
        }
        return success($resp);
    }


    public function userPointStatistics()
    {
        $params            = request()->all();
        $statisticsService = new StatisticsService();
        $resp              = $statisticsService->userPointStatistics($params);
        if ($statisticsService->hasError()) {
            return error($statisticsService->getError());
        }
        return success($resp);
    }

    public function userVisitStatistics()
    {
        $params            = request()->all();
        $statisticsService = new StatisticsService();
        $resp              = $statisticsService->userVisitStatistics($params);
        if ($statisticsService->hasError()) {
            return error($statisticsService->getError());
        }
        return success($resp);
    }


    public function dataCountStatistics()
    {
        $params            = request()->all();
        $statisticsService = new StatisticsService();
        $resp              = $statisticsService->dataCountStatistics($params);
        if ($statisticsService->hasError()) {
            return error($statisticsService->getError());
        }
        return success($resp);
    }

    public function bigScreenLeaderStatistics()
    {
        $params            = request()->all();
        $statisticsService = new StatisticsService();
        $resp              = $statisticsService->bigScreenLeaderStatistics($params);
        if ($statisticsService->hasError()) {
            return error($statisticsService->getError());
        }
        return success($resp);
    }

    public function bigScreenLeaderDistrictStatistics()
    {
        $params            = request()->all();
        $statisticsService = new StatisticsService();
        $resp              = $statisticsService->bigScreenUserStatistics($params);
        if ($statisticsService->hasError()) {
            return error($statisticsService->getError());
        }
        return success($resp);
    }

    public function userCallStatistics()
    {
        $params            = request()->all();
        $statisticsService = new StatisticsService();
        $resp              = $statisticsService->userCallStatistics($params);
        if ($statisticsService->hasError()) {
            return error($statisticsService->getError());
        }
        return success($resp);
    }

    public function schoolPersonChart()
    {
        return json_decode('{
    "code": 200,
    "message": "success",
    "result": {
        "all": {
            "education": [
                {
                    "name": "1",
                    "value": 7700
                },
                {
                    "name": "2",
                    "value": 5130
                },
                {
                    "name": "3",
                    "value": 10182
                }
            ],
            "age": [
                {
                    "name": "17",
                    "value": 1443
                },
                {
                    "name": "18",
                    "value": 2538
                },
                {
                    "name": "19",
                    "value": 2535
                },
                {
                    "name": "20",
                    "value": 2540
                },
                {
                    "name": "21",
                    "value": 2545
                },
                {
                    "name": "22",
                    "value": 2559
                },
                {
                    "name": "23",
                    "value": 2515
                },
                {
                    "name": "24",
                    "value": 2613
                },
                {
                    "name": "25",
                    "value": 2612
                },
                {
                    "name": "26",
                    "value": 1112
                }
            ],
            "sex": [
                {
                    "name": "1",
                    "value": 11536
                },
                {
                    "name": "2",
                    "value": 11476
                }
            ],
            "political_outlook": [
                {
                    "name": "党员",
                    "value": 10
                },
                {
                    "name": "共产党员",
                    "value": 4589
                },
                {
                    "name": "共青团员",
                    "value": 4539
                },
                {
                    "name": "其他",
                    "value": 4556
                },
                {
                    "name": "无党派人士",
                    "value": 4653
                },
                {
                    "name": "群众",
                    "value": 4665
                }
            ],
            "is_beijing": [
                {
                    "name": "1",
                    "value": 766
                },
                {
                    "name": "2",
                    "value": 22246
                }
            ],
            "graduate": [
                {
                    "name": "3",
                    "value": 23012
                }
            ]
        },
        "graduate": {
            "education": [
                {
                    "name": "1",
                    "value": 7700
                },
                {
                    "name": "2",
                    "value": 5130
                },
                {
                    "name": "3",
                    "value": 10182
                }
            ],
            "age": [
                {
                    "name": "17",
                    "value": 1443
                },
                {
                    "name": "18",
                    "value": 2538
                },
                {
                    "name": "19",
                    "value": 2535
                },
                {
                    "name": "20",
                    "value": 2540
                },
                {
                    "name": "21",
                    "value": 2545
                },
                {
                    "name": "22",
                    "value": 2559
                },
                {
                    "name": "23",
                    "value": 2515
                },
                {
                    "name": "24",
                    "value": 2613
                },
                {
                    "name": "25",
                    "value": 2612
                },
                {
                    "name": "26",
                    "value": 1112
                }
            ],
            "sex": [
                {
                    "name": "1",
                    "value": 11536
                },
                {
                    "name": "2",
                    "value": 11476
                }
            ],
            "political_outlook": [
                {
                    "name": "党员",
                    "value": 10
                },
                {
                    "name": "共产党员",
                    "value": 4589
                },
                {
                    "name": "共青团员",
                    "value": 4539
                },
                {
                    "name": "其他",
                    "value": 4556
                },
                {
                    "name": "无党派人士",
                    "value": 4653
                },
                {
                    "name": "群众",
                    "value": 4665
                }
            ],
            "is_beijing": [
                {
                    "name": "1",
                    "value": 766
                },
                {
                    "name": "2",
                    "value": 22246
                }
            ],
            "graduate": [
                {
                    "name": "3",
                    "value": 23012
                }
            ]
        },
        "graduate_before": {
            "education": [],
            "age": [],
            "sex": [],
            "political_outlook": [],
            "is_beijing": [],
            "graduate": []
        },
        "just_graduate": {
            "education": [],
            "age": [],
            "sex": [],
            "political_outlook": [],
            "is_beijing": [],
            "graduate": []
        },
        "updated_at": "2025-06-06 17:27:23 (2 天前)"
    }
}');
    }


    public function missionPromoteCompareList()
    {
        $params  = request()->input();
        $service = new MissionPromoteCompareStatisticsService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    public function missionIntentionList()
    {
        $params  = request()->input();
        $service = new MissionIntentionStatisticsService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    public function missionPoliticalExamList()
    {
        $params  = request()->input();
        $service = new MissionPoliticalExamStatisticsService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    public function missionEducationList()
    {
        $params  = request()->input();
        $service = new MissionEducationStatisticsService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    public function missionPreStoreList()
    {
        $params  = request()->input();
        $service = new MissionPreStoreStatisticsService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    public function missionTaskNumList()
    {
        $params  = request()->input();
        $service = new MissionTaskNumStatisticsService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    // missionPhysicalTaskNumList
    public function missionPhysicalTaskNumList()
    {
        $params  = request()->input();
        $service = new MissionPhysicalTaskNumStatisticsService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    // missionPhysicalCheckList
    public function missionPhysicalCheckList()
    {
        $params  = request()->input();
        $service = new MissionPhysicalCheckStatisticsService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    // missionPhysicalRecheckList
    public function missionPhysicalRecheckList()
    {
        $params  = request()->input();
        $service = new MissionPhysicalRecheckStatisticsService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    // missionPhysicalSpotCheckList
    public function missionPhysicalSpotCheckList()
    {
        $params  = request()->input();
        $service = new MissionPhysicalSpotCheckStatisticsService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    public function missionSummaryList()
    {
        $params  = request()->input();
        $service = new MissionSummaryStatisticsService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    public function missionGoList()
    {
        $params  = request()->input();
        $service = new MissionGoStatisticsService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    public function missionDetail()
    {
        $params  = request()->input();
        $service = new MissionDetailStatisticsService();
        $resp    = $service->getStatistics($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }


    // missionSchoolPersonList
    public function missionSchoolPersonList()
    {
        $params  = request()->input();
        $service = new MissionSchoolPersonStatisticsService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    // missionSchoolPersonChart
    public function missionSchoolPersonChart()
    {
        $params  = request()->input();
        $service = new MissionSchoolPersonStatisticsService();
        $resp    = $service->chart($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    // mission_social_person_list
    public function missionSocialPersonList()
    {
        $params  = request()->input();
        $service = new MissionSocialPersonStatisticsService();
        $resp    = $service->list($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    // mission_social_person_chart
    public function missionSocialPersonChart()
    {
        $params  = request()->input();
        $service = new MissionSocialPersonStatisticsService();
        $resp    = $service->chart($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }
}
