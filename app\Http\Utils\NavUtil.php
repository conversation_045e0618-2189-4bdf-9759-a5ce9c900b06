<?php

namespace App\Http\Utils;

use App\Http\Consts\PermissionSlugConst;

class NavUtil
{
    public static function getNav()
    {
        $user = auth()->user();
        return [
            "list" => [
                // 1. 首页
                [
                    "path"      => "/dashboard",
                    "name"      => "Dashboard",
                    "component" => "LAYOUT",
                    "meta"      => [
                        "title"  => "首页",
                        "icon"   => "home",
                        "hidden" => false,
                    ],
                    "redirect"  => "/dashboard/base",
                    "children"  => [
                        // 首页概况（原概览仪表盘）
                        [
                            "path"      => "base",
                            "name"      => "DashboardBase",
                            "component" => "/dashboard/base/index",
                            "meta"      => [
                                "title"  => "首页概况",
                                "hidden" => false,
                            ]
                        ],
                        // 学习进度统计
                        [
                            "path"      => "study-progress",
                            "name"      => "StudyProgress",
                            "component" => "BLANK",
                            "meta"      => [
                                "title"  => "学习进度统计",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_statistics_course_list,
                                    PermissionSlugConst::admin_statistics_department_course_list,
                                    PermissionSlugConst::admin_statistics_user_course_list,
                                ]),
                            ],
                            "children"  => [
                                // 课程学习进度统计
                                [
                                    "path"      => "course",
                                    "name"      => "CourseCourseList",
                                    "component" => "/statistics/course/course",
                                    "meta"      => [
                                        "title"  => "课程学习进度统计",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_statistics_course_list,
                                        ])
                                    ]
                                ],
                                // 部门学习进度统计
                                [
                                    "path"      => "department",
                                    "name"      => "DepartmentCourseList",
                                    "component" => "/statistics/course/departmentCourse",
                                    "meta"      => [
                                        "title"  => "部门学习进度统计",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_statistics_department_course_list,
                                        ]),
                                    ]
                                ],
                                // 人员学习进度统计
                                [
                                    "path"      => "user",
                                    "name"      => "UserCourseList",
                                    "component" => "/statistics/course/userCourse",
                                    "meta"      => [
                                        "title"  => "人员学习进度统计",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_statistics_user_course_list,
                                        ]),
                                    ]
                                ],
                            ]
                        ],
                        // 考试统计
                        [
                            "path"      => "exam-statistics",
                            "name"      => "ExamStatistics",
                            "component" => "BLANK",
                            "meta"      => [
                                "title"  => "考试统计",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_statistics_department_paper_round_list,
                                    PermissionSlugConst::admin_statistics_user_paper_round_list,
                                ]),
                            ],
                            "children"  => [
                                // 部门考试统计
                                [
                                    "path"      => "department",
                                    "name"      => "DepartmentPaperList",
                                    "component" => "/statistics/paper/departmentPaper",
                                    "meta"      => [
                                        "title"  => "部门考试统计",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_statistics_department_paper_round_list,
                                        ]),
                                    ]
                                ],
                                // 人员考试统计
                                [
                                    "path"      => "user",
                                    "name"      => "UserPaperList",
                                    "component" => "/statistics/paper/userPaper",
                                    "meta"      => [
                                        "title"  => "人员考试统计",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_statistics_user_paper_round_list,
                                        ])
                                    ]
                                ]
                            ]
                        ],
                    ]
                ],
                // 2. 人员潜力
                [
                    "path"      => "/personnel-potential",
                    "name"      => "PersonnelPotential",
                    "component" => "LAYOUT",
                    "redirect"  => "/personnel-potential/regular-work",
                    "meta"      => [
                        "title"  => "人员潜力",
                        "icon"   => "user-circle",
                        "hidden" => !$user->hasPermission([
                            PermissionSlugConst::admin_mission_show,
                            PermissionSlugConst::admin_recall_task_show,
                        ]),
                    ],
                    "children"  => [
                        // 平时工作（原征兵任务）
                        [
                            "path"      => "regular-work",
                            "name"      => "RegularWork",
                            "component" => "/mission/list",
                            "meta"      => [
                                "title"  => "平时工作",
                                "hidden" => !$user->hasPermission([PermissionSlugConst::admin_mission_show]),
                            ]
                        ],
                        // 特殊时期工作（原特殊时期征召）
                        [
                            "path"      => "special-work",
                            "name"      => "SpecialWork",
                            "component" => "/recall/task/list",
                            "meta"      => [
                                "title"  => "特殊时期工作",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_recall_task_show,
                                ]),
                            ]
                        ]
                    ]
                ],
                // 3. 日常办公
                [
                    "path"      => "/daily-office",
                    "name"      => "DailyOffice",
                    "component" => "LAYOUT",
                    "redirect"  => "/daily-office/notice-announcement",
                    "meta"      => [
                        "title"  => "日常办公",
                        "icon"   => "work",
                        "hidden" => !$user->hasPermission([
                            PermissionSlugConst::admin_work_task_show,
                            PermissionSlugConst::admin_work_task_type_show,
                            PermissionSlugConst::admin_inform_show,
                            PermissionSlugConst::admin_survey_show,
                        ])
                    ],
                    "children"  => [
                        // 通知公告（合并发布任务和通知发布）
                        [
                            "path"      => "notice-announcement",
                            "name"      => "NoticeAnnouncement",
                            "component" => "/work/notice/create_notice",
                            "meta"      => [
                                "title"  => "通知公告",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_work_task_show,
                                    PermissionSlugConst::admin_inform_show,
                                    PermissionSlugConst::admin_work_task_store,
                                    PermissionSlugConst::admin_inform_store,
                                ]),
                            ],
                        ],
                        // 待办任务
                        [
                            "path"      => "todo-task",
                            "name"      => "WorkTodoTask",
                            "component" => "/work/task/todo_task",
                            "meta"      => [
                                "title"  => "待办任务",
                                "hidden" => !$user->hasPermission([
                                        PermissionSlugConst::admin_work_task_show,
                                    ]) || $user->hasRole(config("system.city_admin_role_slug")),
                            ]
                        ],
                        // 通知我的
                        [
                            "path"      => "todo-inform",
                            "name"      => "WorkTodoInform",
                            "component" => "/work/inform/todo_inform",
                            "meta"      => [
                                "title"  => "通知我的",
                                "hidden" => !$user->hasPermission([
                                        PermissionSlugConst::admin_inform_show,
                                    ]) || $user->hasRole(config("system.city_admin_role_slug")),
                            ]
                        ],
                        // 工作类型
                        [
                            "path"      => "work-type",
                            "name"      => "WorkTaskType",
                            "component" => "/basic/category/workTaskCategoryList",
                            "meta"      => [
                                "title"  => "工作类型",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_work_task_type_show,
                                ]),
                            ]
                        ],
                        // 调查问卷
                        [
                            "path"      => "survey",
                            "name"      => "WorkListSurvey",
                            "component" => "/work/survey/index",
                            "meta"      => [
                                "title"  => "调查问卷",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_survey_show,
                                ]),
                            ]
                        ],
                        // 调查问卷-创建（隐藏页面）
                        [
                            "path"      => "create-survey",
                            "name"      => "WorkCreateSurvey",
                            "component" => "/work/survey/steps",
                            "meta"      => [
                                "title"  => "创建调查问卷",
                                "hidden" => true,
                            ]
                        ],
                    ]
                ],
                // 4. 智能办公和回访
                [
                    'path'      => '/intelligent-office',
                    'name'      => 'IntelligentOffice',
                    'component' => 'LAYOUT',
                    'redirect'  => '/intelligent-office/sms-template',
                    'meta'      => [
                        'title'  => '智能办公和回访',
                        'icon'   => 'logo-android',
                        'hidden' => !$user->hasPermission([
                            PermissionSlugConst::admin_sms_template_show,
                            PermissionSlugConst::admin_vms_voice_template_show,
                            PermissionSlugConst::admin_ccs_robot_template_show,
                            PermissionSlugConst::admin_out_notice_task_show,
                        ])
                    ],
                    'children'  => [
                        [
                            'path'      => 'sms-template',
                            'name'      => 'IntelligentSmsTemplate',
                            'component' => '/outNotice/smsTemplate/list',
                            'meta'      => [
                                'title'  => '短信模板',
                                'hidden' => !$user->hasPermission([
                                    PermissionSlugConst::admin_sms_template_show,
                                ]),
                            ]
                        ],
                        // 语音通知模板
                        [
                            'path'      => 'voice-template',
                            'name'      => 'IntelligentVoiceTemplate',
                            'component' => '/outNotice/voiceTemplate/list',
                            'meta'      => [
                                'title'  => '语音模板',
                                'hidden' => !$user->hasPermission([
                                    PermissionSlugConst::admin_vms_voice_template_show,
                                ]),
                            ],
                            'children'  => [
                                [
                                    'path'      => '/intelligent-office/voice-template/create',
                                    'name'      => 'IntelligentVoiceTemplateCreate',
                                    'component' => '/outNotice/voiceTemplate/create',
                                    'meta'      => [
                                        'title'  => '创建语音模板',
                                        'hidden' => true,
                                    ]
                                ],
                            ]
                        ],
                        // 机器人话术模板
                        [
                            'path'      => 'robot-template',
                            'name'      => 'IntelligentRobotTemplate',
                            'component' => '/outNotice/robotTemplate/list',
                            'meta'      => [
                                'title'  => '机器人话术模板',
                                'hidden' => !$user->hasPermission([
                                    PermissionSlugConst::admin_ccs_robot_template_show,
                                ]),
                            ],
                        ],
                        // 任务
                        [
                            'path'      => 'task',
                            'name'      => 'IntelligentTask',
                            'component' => '/outNotice/task/list',
                            'meta'      => [
                                'title'  => '任务',
                                'hidden' => !$user->hasPermission([
                                    PermissionSlugConst::admin_out_notice_task_show,
                                ])
                            ]
                        ],
                    ]
                ],
                // 5. 课程管理
                [
                    "path"      => "/course",
                    "name"      => "Course",
                    "component" => "LAYOUT",
                    "redirect"  => "/course/course-list",
                    "meta"      => [
                        "title"  => "课程管理",
                        "icon"   => "course",
                        "hidden" => !$user->hasPermission([
                            PermissionSlugConst::admin_stage_show,
                            PermissionSlugConst::admin_course_type_show,
                            PermissionSlugConst::admin_course_show,
                            PermissionSlugConst::admin_chapter_show,
                            PermissionSlugConst::admin_course_type_show,
                        ]),
                    ],
                    "children"  => [
                        // 学年管理
                        [
                            "path"      => "stage",
                            "name"      => "stage",
                            "component" => "/course/stage/list",
                            "meta"      => [
                                "title"  => "学年管理",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_stage_show,
                                ]),
                            ]
                        ],
                        // 课程类型
                        [
                            "path"      => "course_type",
                            "name"      => "courseTypeCategory",
                            "component" => "/basic/category/courseTypeCategoryList",
                            "meta"      => [
                                "title"  => "课程类型",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_course_type_show,
                                ]),
                            ]
                        ],
                        // 课程管理
                        [
                            "path"      => "course-list",
                            "name"      => "CourseList",
                            "component" => "/course/course",
                            "meta"      => [
                                "title"  => "课程列表",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_course_show,
                                ]),
                            ]
                        ],
                        [
                            "path"      => "=>course_id/chapter",
                            "name"      => "CourseChapter",
                            "component" => "/course/chapter",
                            "meta"      => [
                                "title"  => "目录管理",
                                "hidden" => true,
                            ]
                        ],
                        // 课件管理
                        [
                            "path"      => "courseware",
                            "name"      => "Courseware",
                            "component" => "/course/courseware",
                            "meta"      => [
                                "title"  => "课件管理",
                                "hidden" => false,
                            ]
                        ],
                    ]
                ],
                // 6. 练习考核
                [
                    "path"      => "/practice-assessment",
                    "name"      => "PracticeAssessment",
                    "component" => "LAYOUT",
                    "redirect"  => "/practice-assessment/question",
                    "meta"      => [
                        "title"  => "练习考核",
                        "icon"   => "book",
                        "hidden" => !$user->hasPermission([
                            PermissionSlugConst::admin_question_show,
                            PermissionSlugConst::admin_knowledge_show,
                            PermissionSlugConst::admin_paper_show,
                        ]),
                    ],
                    "children"  => [
                        [
                            "path"      => "question",
                            "name"      => "Question",
                            "component" => "/paper/question/question",
                            "meta"      => [
                                "title"  => "题库管理",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_question_show,
                                ]),
                            ]
                        ],
                        [
                            "path"      => "knowledge",
                            "name"      => "Knowledge",
                            "component" => "/paper/question/knowledge",
                            "meta"      => [
                                "title"  => "知识点管理",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_knowledge_show,
                                ]),
                            ]
                        ],
                        [
                            "path"      => "exam",
                            "name"      => "Paper",
                            "component" => "/paper/exam",
                            "meta"      => [
                                "title"  => "认证考核管理",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_paper_show,
                                ]),
                            ]
                        ],
                        [
                            "path"      => "assessment",
                            "name"      => "Assessment",
                            "component" => "/paper/assessment",
                            "meta"      => [
                                "title"  => "小结考核管理",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_paper_show,
                                ]),
                            ]
                        ],
                        [
                            "path"      => "practice",
                            "name"      => "Practice",
                            "component" => "/paper/practice",
                            "meta"      => [
                                "title"  => "练习管理",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_paper_show,
                                ]),
                            ]
                        ],
                    ]
                ],
                // 7. 系统设置
                [
                    "path"      => "/system-settings",
                    "name"      => "SystemSettings",
                    "component" => "LAYOUT",
                    "meta"      => [
                        "title"  => "系统设置",
                        "icon"   => "setting",
                        "hidden" => !$user->hasPermission([
                            PermissionSlugConst::admin_config_post,
                            PermissionSlugConst::admin_role_show,
                            PermissionSlugConst::admin_permission_show,
                            PermissionSlugConst::admin_operation_log_show,
                            PermissionSlugConst::admin_user_show,
                            PermissionSlugConst::admin_region_show,
                            PermissionSlugConst::admin_business_category_show,
                            PermissionSlugConst::admin_invite_code_show,
                            PermissionSlugConst::admin_category_show,
                            PermissionSlugConst::admin_article_show,
                            PermissionSlugConst::admin_menu_show,
                            PermissionSlugConst::admin_page_show,
                            PermissionSlugConst::admin_consult_knowledge_show,
                            PermissionSlugConst::admin_consult_file_show,
                        ]),
                    ],
                    "redirect"  => "/system-settings/config",
                    "children"  => [
                        // 系统配置
                        [
                            "path"      => "config",
                            "name"      => "Config",
                            "component" => "/config/index",
                            "meta"      => [
                                "title"  => "系统配置",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_config_get, PermissionSlugConst::admin_config_post,
                                ]),
                            ]
                        ],
                        // 角色管理
                        [
                            "path"      => "role",
                            "name"      => "Role",
                            "component" => "/basic/role/list",
                            "meta"      => [
                                "title"  => "角色管理",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_role_show,
                                ]),
                            ]
                        ],
                        // 权限管理
                        [
                            "path"      => "permission",
                            "name"      => "Permission",
                            "component" => "/basic/permission/list",
                            "meta"      => [
                                "title"  => "权限管理",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_permission_show,
                                ]),
                            ]
                        ],
                        // 操作记录
                        [
                            "path"      => "operation-log",
                            "name"      => "OperationLog",
                            "component" => "/basic/operationLog/list",
                            "meta"      => [
                                "title"  => "操作记录",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_operation_log_show,
                                ]),
                            ]
                        ],
                        // 用户管理
                        [
                            "path"      => "user-management",
                            "name"      => "UserManagement",
                            "component" => "BLANK",
                            "redirect"  => "/system-settings/user-management/region",
                            "meta"      => [
                                "title"  => "用户管理",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_user_show,
                                    PermissionSlugConst::admin_region_show,
                                    PermissionSlugConst::admin_business_category_show,
                                    PermissionSlugConst::admin_invite_code_show,
                                ]),
                            ],
                            "children"  => [
                                // 部门管理
                                [
                                    "path"      => "region",
                                    "name"      => "Department",
                                    "component" => "/basic/region/list",
                                    "meta"      => [
                                        "title"  => "部门管理",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_region_show,
                                        ]),
                                    ]
                                ],
                                // 业务类型
                                [
                                    "path"      => "business-type",
                                    "name"      => "BusinessType",
                                    "component" => "/basic/category/businessCategoryList",
                                    "meta"      => [
                                        "title"  => "业务类型",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_business_category_show,
                                        ]),
                                    ]
                                ],
                                [
                                    "path"      => "user-list",
                                    "name"      => "UserList",
                                    "component" => "/basic/user/list",
                                    "meta"      => [
                                        "title"  => "用户列表",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_user_show,
                                        ]),
                                    ]
                                ],
                                //授权码管理
                                [
                                    "path"      => "invitation-code",
                                    "name"      => "InvitationCodes",
                                    "component" => "/basic/user/inInvitationCodeList",
                                    "meta"      => [
                                        "title"  => "授权码管理",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_invite_code_show,
                                        ]),
                                    ]
                                ],
                                // 系统通知
                                [
                                    "path"      => "system-notice",
                                    "name"      => "SystemNotice",
                                    "component" => "/basic/user/notification/index",
                                    "meta"      => [
                                        "title"  => "系统通知",
                                        'hidden' => true,
                                    ]
                                ],
                                // 修改密码
                                [
                                    "path"      => "password",
                                    "name"      => "UpdatePassword",
                                    "component" => "/basic/user/password",
                                    "meta"      => [
                                        "title"  => "修改密码",
                                        "hidden" => true,
                                    ]
                                ]
                            ]
                        ],
                        // 基础数据
                        [
                            "path"      => "basic-data",
                            "name"      => "BasicData",
                            "component" => "BLANK",
                            "redirect"  => "/system-settings/basic-data/article-category",
                            "meta"      => [
                                "title"  => "基础数据",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_category_show,
                                    PermissionSlugConst::admin_article_show,
                                    PermissionSlugConst::admin_menu_show,
                                    PermissionSlugConst::admin_page_show,
                                    PermissionSlugConst::admin_consult_knowledge_show,
                                    PermissionSlugConst::admin_consult_file_show,
                                ]),
                            ],
                            "children"  => [
                                [
                                    "path"      => "article-category",
                                    "name"      => "ArticleCategory",
                                    "component" => "/basic/category/articleCategoryList",
                                    "meta"      => [
                                        "title"  => "文章分类",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_category_show,
                                        ]),
                                    ]
                                ],
                                [
                                    "path"      => "article-list",
                                    "name"      => "ArticleList",
                                    "component" => "/basic/article/list",
                                    "meta"      => [
                                        "title"  => "文章",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_article_show,
                                        ]),
                                    ]
                                ],
                                [
                                    "path"      => "article-create",
                                    "name"      => "ArticleCreate",
                                    "component" => "/basic/article/create",
                                    "meta"      => [
                                        "title"  => "文章添加",
                                        "hidden" => true,
                                    ]
                                ],
                                [
                                    "path"      => "article-edit/:id",
                                    "name"      => "ArticleEdit",
                                    "component" => "/basic/article/edit",
                                    "meta"      => [
                                        "title"  => "文章修改",
                                        "hidden" => true
                                    ]
                                ],
                                [
                                    "path"      => "menu",
                                    "name"      => "Menu",
                                    "component" => "/basic/menu/list",
                                    "meta"      => [
                                        "title"  => "菜单",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_menu_show,
                                        ]),
                                    ]
                                ],
                                [
                                    "path"      => "page-list",
                                    "name"      => "PageList",
                                    "component" => "/basic/page/list",
                                    "meta"      => [
                                        "title"  => "页面",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_page_show,
                                        ]),
                                    ]
                                ],
                                [
                                    "path"      => "page-create",
                                    "name"      => "PageCreate",
                                    "component" => "/basic/page/create",
                                    "meta"      => [
                                        "title"  => "页面添加",
                                        "hidden" => true
                                    ]
                                ],
                                [
                                    "path"      => "page-edit/:id",
                                    "name"      => "PageEdit",
                                    "component" => "/basic/page/edit",
                                    "meta"      => [
                                        "title"  => "页面修改",
                                        "hidden" => true
                                    ]
                                ],
                                [
                                    "path"      => "consult-knowledge-list",
                                    "name"      => "ConsultKnowledgeList",
                                    "component" => "/basic/consultKnowledge/list",
                                    "meta"      => [
                                        "title"  => "咨询问答",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_consult_knowledge_show,
                                        ]),
                                    ]
                                ],
                                [
                                    "path"      => "consult-knowledge-create",
                                    "name"      => "ConsultKnowledgeCreate",
                                    "component" => "/basic/consultKnowledge/create",
                                    "meta"      => [
                                        "title"  => "咨询问答",
                                        "hidden" => true
                                    ]
                                ],
                                [
                                    "path"      => "consult-knowledge-edit/:id",
                                    "name"      => "ConsultKnowledgeEdit",
                                    "component" => "/basic/consultKnowledge/edit",
                                    "meta"      => [
                                        "title"  => "咨询问答修改",
                                        "hidden" => true
                                    ]
                                ],
                                [
                                    "path"      => "ai-database",
                                    "name"      => "AiDatabase",
                                    "component" => "/basic/consultFile/list",
                                    "meta"      => [
                                        "title"  => "大模型数据库",
                                        "hidden" => !$user->hasPermission([
                                            PermissionSlugConst::admin_consult_file_show,
                                        ]),
                                    ]
                                ],
                            ]
                        ],
                    ]
                ],
                // 其他可能需要保留的隐藏页面或特殊路由
                // 举报管理 - 如果需要保留
                [
                    "path"      => "/report",
                    "name"      => "Report",
                    "component" => "LAYOUT",
                    "redirect"  => "/report/list",
                    "meta"      => [
                        "title"  => "举报管理",
                        "icon"   => "shield-error",
                        "hidden" => !$user->hasPermission([
                            PermissionSlugConst::admin_report_store,
                            PermissionSlugConst::admin_report_reply,
                        ])
                    ],
                    "children"  => [
                        [
                            "path"      => "list",
                            "name"      => "ReportList",
                            "component" => "/report/todo_report",
                            "meta"      => [
                                "title"  => "待处理举报",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_report_reply,
                                ]),
                            ]
                        ],
                        // 发起举报
                        [
                            "path"      => "create",
                            "name"      => "ReportCreate",
                            "component" => "/report/create",
                            "meta"      => [
                                "title"  => "发起举报",
                                "hidden" => !$user->hasPermission([
                                    PermissionSlugConst::admin_report_store,
                                ]),
                            ]
                        ],
                    ]
                ],
                // 其他统计页面（如积分统计、拨打电话统计等）
                [
                    'path'      => '/other-statistics',
                    'name'      => 'OtherStatistics',
                    'component' => 'LAYOUT',
                    'meta'      => [
                        'title'  => '其他统计',
                        'icon'   => 'chart-analytics',
                        'hidden' => !$user->hasPermission([
                            PermissionSlugConst::admin_statistics_user_point_list,
                            PermissionSlugConst::admin_statistics_user_call_list,
                        ])
                    ],
                    'children'  => [
                        // 积分统计
                        [
                            'path'      => 'point',
                            'name'      => 'Point',
                            "component" => "/statistics/user/point",
                            'meta'      => [
                                'title'  => '积分统计',
                                'hidden' => !$user->hasPermission([
                                    PermissionSlugConst::admin_statistics_user_point_list,
                                ]),
                            ],
                        ],
                        // 拨打电话统计
                        [
                            'path'      => 'call',
                            'name'      => 'Call',
                            "component" => "/statistics/user/call",
                            'meta'      => [
                                'title'  => '拨打电话统计',
                                'hidden' => !$user->hasPermission([
                                    PermissionSlugConst::admin_statistics_user_call_list,
                                ]),
                            ],
                        ],
                    ],
                ],
                // 保留任务相关的详细页面路由（隐藏的子页面）
                [
                    'path'      => '/mission',
                    'name'      => 'Mission',
                    "component" => "LAYOUT",
                    "redirect"  => "/mission/list",
                    'meta'      => [
                        'title'  => '任务管理',
                        'icon'   => 'book',
                        'single' => true,
                        "hidden" => true, // 隐藏主菜单，但保留路由用于详情页面
                    ],
                    'children'  => [
                        [
                            'path'      => 'list',
                            'name'      => 'missionList',
                            'component' => '/mission/list',
                            'meta'      => [
                                'title' => '任务列表',
                                'icon'  => 'book',
                                'hidden' => true,
                            ],
                        ],
                        [
                            "path"      => "detail/:id",
                            "name"      => "MissionDetail",
                            "component" => "Blank",
                            "meta"      => [
                                "title"  => "任务详情",
                                "hidden" => true,
                            ],
                            "children"  => [
                                // 任务详情页面的所有子路由保持不变
                                [
                                    "path"      => "index",
                                    "name"      => "任务详情",
                                    "component" => "/mission/detail",
                                    "meta"      => [
                                        "title"  => "任务详情",
                                        "hidden" => true,
                                    ],
                                ],
                                [
                                    "path"      => "person_list",
                                    "name"      => "PersonList",
                                    "component" => "/mission/setting/person/index",
                                    "meta"      => [
                                        "title"  => "人员名单",
                                        "hidden" => true,
                                    ],
                                ],
                                [
                                    "path"      => "number",
                                    "name"      => "任务数设置",
                                    "component" => "/mission/setting/number/index",
                                    "meta"      => [
                                        "title"  => "任务数设置",
                                        "hidden" => true,
                                    ],
                                ],
                                [
                                    "path"      => "intention",
                                    "name"      => "意向统计",
                                    "component" => "/mission/process/intention/index",
                                    "meta"      => [
                                        "title"  => "意向统计",
                                        "hidden" => true,
                                    ],
                                ],
                                [
                                    "path"      => "promote_compare",
                                    "name"      => "宣传对比",
                                    "component" => "/mission/process/promoteCompare/index",
                                    "meta"      => [
                                        "title"  => "宣传对比",
                                        "hidden" => true,
                                    ],
                                ],
                                [
                                    "path"      => "physical_examination",
                                    "name"      => "体格检查",
                                    "component" => "/mission/process/physicalExamination/index",
                                    "meta"      => [
                                        "title"  => "体格检查",
                                        "hidden" => true,
                                    ],
                                ],
                                [
                                    "path"      => "political_examination",
                                    "name"      => "政考比对",
                                    "component" => "/mission/process/politicalExamination/index",
                                    "meta"      => [
                                        "title"  => "政考比对",
                                        "hidden" => true,
                                    ],
                                ],
                                [
                                    "path"      => "education",
                                    "name"      => "役前教育",
                                    "component" => "/mission/process/education/index",
                                    "meta"      => [
                                        "title"  => "役前教育",
                                        "hidden" => true,
                                    ],
                                ],
                                [
                                    "path"      => "go",
                                    "name"      => "走兵",
                                    "component" => "/mission/process/go/index",
                                    "meta"      => [
                                        "title"  => "走兵",
                                        "hidden" => true,
                                    ],
                                ],
                                [
                                    "path"      => "pre_store",
                                    "name"      => "预储",
                                    "component" => "/mission/process/preStore/index",
                                    "meta"      => [
                                        "title"  => "预储",
                                        "hidden" => true,
                                    ],
                                ],
                                [
                                    "path"      => "statistics",
                                    "name"      => "数据汇总",
                                    "component" => "/mission/process/statistics/department",
                                    "meta"      => [
                                        "title"  => "数据汇总",
                                        "hidden" => true,
                                    ],
                                ],
                                [
                                    "path"      => "return",
                                    "name"      => "退兵名单",
                                    "component" => "/mission/process/return/index",
                                    "meta"      => [
                                        "title"  => "退兵名单",
                                        "hidden" => true,
                                    ],
                                ],
                            ],
                        ]
                    ]
                ],
            ]
        ];
    }
}
