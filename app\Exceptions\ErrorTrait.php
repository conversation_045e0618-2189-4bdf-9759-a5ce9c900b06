<?php

namespace App\Exceptions;

/**
 * 错误信息Trait类
 */
trait ErrorTrait
{
    /**
     * 错误码
     * @var string
     */
    protected $code = 0;

    /**
     * 错误信息
     * @var string
     */
    protected $error = '';

    /**
     * 设置错误信息
     * @param string $error
     * @param int $code
     * @return bool
     */
    protected function setError(string $error, int $code = 0): bool
    {
        $this->error = $error ?: '未知错误';
        $this->code  = $code;
        return false;
    }

    /**
     * 获取错误信息
     * @return string
     */
    public function getError(): string
    {
        return $this->error;
    }

    /**
     * 是否存在错误信息
     * @return bool
     */
    public function hasError(): bool
    {
        return !empty($this->error);
    }

    /**
     * 设置错误码
     * @param string $code
     * @return bool
     */
    protected function setCode(string $code): bool
    {
        $this->code = $code ?: 0;
        return false;
    }

    /**
     * 获取错误码
     * @return int
     */
    public function getCode(): int
    {
        return $this->code;
    }
}
