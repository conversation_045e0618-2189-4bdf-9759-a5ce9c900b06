<?php

namespace App\Http\Traits;

use App\Models\Attachment;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\IOFactory;

trait ExcelTraits
{
    /**
     * 导出数据
     * @throws Exception
     */
    public function saveExcel($filename, $headers, $data, $path = 'php://output')
    {
        $cellName = array(
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ'
        );

        $spreadsheet = new Spreadsheet();
        $spreadsheet->setActiveSheetIndex(0);

        $sheet = $spreadsheet->getActiveSheet();
        //表头长度
        $title_count = count($headers);
        for ($i = 0; $i < $title_count; $i++) {
            $sheet->setCellValue($cellName[$i] . '1', $headers[$i]);
        }
        //n列数据
        $column = $i;
        //行下标
        $index = 0;
        foreach ($data as $row_index => $row_value) {
            //写入excel第n行
            $index = $row_index + 2;
            //列下标
            $cell_index = 0;
            foreach ($row_value as $k => $v) {
                //设置第n行数据的A,B,C...列
                $sheet->setCellValue($cellName[$cell_index] . $index, $v);
                $cell_index++;
            }
        }

        for ($i = 0; $i < $column; $i++) {
            $spreadsheet->getActiveSheet()->getColumnDimension($cellName[$i])->setWidth(20);
        }

        //n行数据加1行表头
        $row = $index;
        // 设置垂直居中
        $spreadsheet->getActiveSheet()->getStyle("A1:" . ($cellName[$column - 1]) . $row)->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        // 设置水平居中
        $spreadsheet->getActiveSheet()->getStyle("A1:" . ($cellName[$column - 1]) . $row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
        // 设置自动换行
        $spreadsheet->getActiveSheet()->getStyle("A1:" . ($cellName[$column - 1]) . $row)->getAlignment()->setWrapText(true);

        if ($path == 'php://output') {
            header("Content-Disposition: attachment;filename=$filename.xlsx");
            header('Content-Type: applicsation/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('filename: ' . urlencode($filename) . '.xlsx');
            $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
            ob_end_clean();
            $writer->save('php://output');
        } else {
            $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
            $writer->save($path);
        }
    }

    public function readExcel($filePath)
    {
        $spreadsheet = IOFactory::load($filePath);
        //$worksheet = $spreadsheet->getActiveSheet();   //获取当前文件内容
//        $sheetAllCount = $spreadsheet->getSheetCount(); // 工作表总数
//        for ($index = 0; $index < $sheetAllCount; $index++) {   //工作表标题
//            $title[] = $spreadsheet->getSheet($index)->getTitle();
//        }
        //读取第一個工作表
        $whatTable          = 0;
        $sheet              = $spreadsheet->getSheet($whatTable);
        $highest_row        = $sheet->getHighestRow(); // 取得总行数
        $highest_column     = $sheet->getHighestColumn(); ///取得列数  字母abc...
        $highestColumnIndex = Coordinate::columnIndexFromString($highest_column);  //转化为数字;
        for ($i = 1; $i <= $highestColumnIndex; $i++) {
            for ($j = 1; $j <= $highest_row; $j++) {
                $content      = $sheet->getCellByColumnAndRow($i, $j)->getCalculatedValue();
                $info[$j][$i] = $content;
            }
        }
        return $info;
    }

    public function readExcelFormAttachmentId($attachmentId)
    {
        $attachment = Attachment::query()->find($attachmentId);
        $fileUrl    = $attachment->full_path;
        $tempFolder = storage_path('app/public/temp/');
        $tempPath   = $tempFolder . Str::random() . '.xlsx';
        if (!file_exists($tempFolder)) {
            mkdir($tempFolder, 0777, true);
        }
        file_put_contents($tempPath, file_get_contents($fileUrl));
        $data = $this->readExcel($tempPath);
        unlink($tempPath);
        return $data;
    }
}
