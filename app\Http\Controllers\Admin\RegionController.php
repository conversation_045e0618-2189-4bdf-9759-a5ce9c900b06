<?php

namespace App\Http\Controllers\Admin;

use App\Http\Services\Admin\RegionService;
use Illuminate\Http\Request;

class RegionController
{
    public function index(Request $request)
    {
        $service  = new RegionService();
        $params   = $request->all();
        $response = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function store(Request $request)
    {
        $service  = new RegionService();
        $response = $service->store($request->all());
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function show($id, Request $request)
    {
        $service  = new RegionService();
        $response = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function update($id, Request $request)
    {
        $service  = new RegionService();
        $response = $service->update($id, $request->all());
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function destroy($id, Request $request)
    {
        $service  = new RegionService();
        $response = $service->destroy($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function tree()
    {
        $params   = request()->input();
        $service  = new RegionService();
        $response = $service->tree($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }
}
