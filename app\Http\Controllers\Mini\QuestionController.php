<?php

namespace App\Http\Controllers\Mini;

use App\Http\Services\Mini\QuestionService;

class QuestionController
{
    public function show($id)
    {
        $params  = request()->input();
        $service = new QuestionService();
        $resp    = $service->show($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
