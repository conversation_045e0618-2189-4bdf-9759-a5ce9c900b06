<?php

namespace App\Console\Commands;

use App\Http\Consts\RegionConst;
use App\Http\Traits\ExcelTraits;
use App\Models\Region;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ImportHospital extends Command
{
    use ExcelTraits;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-hospital';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $pinyin               = app('pinyin');
        $schoolExcelPath      = resource_path('docs/区-医院.xlsx');
        $rows                 = $this->readExcel($schoolExcelPath);
        $parentDepartmentName = null;
        $districtName         = null;
        DB::beginTransaction();
        try {
            foreach ($rows as $row) {
                if ($row[1]) {
                    $parentDepartmentName = $row[1];
                    $districtName         = $parentDepartmentName;
                }
                $childDepartmentName = $row[2];

                $district = Region::query()->where('name', $districtName)->where('level', RegionConst::levelDistrict)->first();
                if (!$district) {
                    $this->info("district not found: $districtName");
                    throw new \Exception("district not found: $districtName");
                }
                $parentDepartmentName = "医院";
                $parentDepartment     = Region::query()->where('p_code', $district->code)
                    ->where('name', $parentDepartmentName)
                    ->where('level', RegionConst::levelHospital)->first();
                if (!$parentDepartment) {
                    $code = Str::replace(' ', '', $pinyin->abbr($parentDepartmentName));
                    while (Region::query()->where('code', $code)->exists()) {
                        $code .= '2';
                    }
                    if (Region::query()->where('code', $code)->exists()) {
                        $this->info("parentDepartmentName: $parentDepartmentName, code: $code exists");
                        throw new \Exception("parentDepartmentName: $parentDepartmentName, code: $code exists");
                    }
                    echo "create parentDepartment: $parentDepartmentName, code: $code" . PHP_EOL;
                    $parentDepartment = Region::query()->create([
                        'code'   => $code,
                        'p_code' => $district->code,
                        'name'   => $parentDepartmentName,
                        'level'  => RegionConst::levelHospital,
                        'depth'  => $district->depth + 1,
                    ]);
                }
                $childDepartment = Region::query()->where('name', $childDepartmentName)->where('level', RegionConst::levelHospital)->first();
                if (!$childDepartment) {
                    $code = Str::replace(' ', '', $pinyin->abbr($childDepartmentName));
                    while (Region::query()->where('code', $code)->exists()) {
                        $code .= '2';
                    }
                    if (Region::query()->where('code', $code)->exists()) {
                        $this->info("childDepartmentName: $childDepartmentName, code: $code exists");
                        throw new \Exception("childDepartmentName: $childDepartmentName, code: $code exists");
                    }
                    echo "create childDepartment: $childDepartmentName, code: $code" . PHP_EOL;
                    $childDepartment = Region::query()->create([
                        'code'   => $code,
                        'p_code' => $parentDepartment->code,
                        'name'   => $childDepartmentName,
                        'level'  => RegionConst::levelHospital,
                        'depth'  => $parentDepartment->depth + 1,
                    ]);
                }
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            dd($exception);
        }
    }
}
