<?php

namespace App\Http\Controllers\Admin\OutNotice;

use App\Http\Services\Admin\OutNotice\OutNoticeTaskReceiveService;
use Illuminate\Http\Request;

class OutNoticeTaskReceiveController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new OutNoticeTaskReceiveService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new OutNoticeTaskReceiveService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new OutNoticeTaskReceiveService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new OutNoticeTaskReceiveService();
        $resp    = $service->update($params, $id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $service  = new OutNoticeTaskReceiveService();
        $params   = $request->all();
        $response = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function clear()
    {
        $params   = request()->input();
        $service  = new OutNoticeTaskReceiveService();
        $response = $service->clear($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function robotStatistics($id)
    {
        $params   = request()->input();
        $service  = new OutNoticeTaskReceiveService();
        $response = $service->robotStatistics($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function run()
    {
        $params   = request()->input();
        $service  = new OutNoticeTaskReceiveService();
        $response = $service->run($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }
}
