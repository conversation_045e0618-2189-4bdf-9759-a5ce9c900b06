<?php

namespace App\Http\Controllers\Admin;

use App\Http\Services\Admin\InvitationCodeService;
use Illuminate\Http\Request;

class InvitationCodeController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new InvitationCodeService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new InvitationCodeService();
        $resp    = $service->update($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $params  = $request->all();
        $service = new InvitationCodeService();
        $resp    = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }


    public function removeAll()
    {
        $params  = request()->all();
        $service = new InvitationCodeService();
        $resp    = $service->removeAll($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
