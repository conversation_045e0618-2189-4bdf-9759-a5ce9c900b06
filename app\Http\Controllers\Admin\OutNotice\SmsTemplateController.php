<?php

namespace App\Http\Controllers\Admin\OutNotice;

use App\Http\Services\Admin\OutNotice\SmsTemplateService;
use Illuminate\Http\Request;

class SmsTemplateController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new SmsTemplateService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new SmsTemplateService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new SmsTemplateService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new SmsTemplateService();
        $resp    = $service->update($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $params  = $request->all();
        $service = new SmsTemplateService();
        $resp    = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }


    public function refresh($id)
    {
        $service = new SmsTemplateService();
        $resp    = $service->refresh($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function getSmsUploadSignature()
    {
        $service = new SmsTemplateService();
        $resp    = $service->getSmsUploadSignature();
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
