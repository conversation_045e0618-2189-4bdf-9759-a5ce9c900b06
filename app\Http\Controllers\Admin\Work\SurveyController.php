<?php

namespace App\Http\Controllers\Admin\Work;

use App\Http\Services\Admin\Work\SurveyService;
use Illuminate\Http\Request;

class SurveyController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new SurveyService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new SurveyService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new SurveyService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new SurveyService();
        $resp    = $service->update($params, $id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $params  = $request->all();
        $service = new SurveyService();
        $resp    = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function statistics($id)
    {
        $params  = request()->all();
        $service = new SurveyService();
        $resp    = $service->statistics($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function qrcode($id)
    {
        $params  = request()->all();
        $service = new SurveyService();
        $resp    = $service->qrcode($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
