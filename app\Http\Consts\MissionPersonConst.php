<?php

namespace App\Http\Consts;

class MissionPersonConst
{
    // Person types
    const personTypeSchool = 1;
    const personTypeSocial = 2;

    // Beijing residency status
    const personIsBeijingUnknown = -1;
    const personIsBeijingYes = 1;
    const personIsBeijingNo = 2;

    // Pre-type status
    const personPreTypeUnknown = -1;
    const personPreTypeDoublePassed = 1;
    const personPreTypeEducation = 2;

    // Study form
    const personStudyFormUnknown = -1;
    const personStudyFormFullTime = 1;
    const personStudyFormNonFullTime = 2;

    // Education level
    const personEducationZhuan = 1;
    const personEducationBen = 2;
    const personEducationBenPlus = 3;

    // Graduate status
    const personGraduateBefore = 1;
    const personGraduateJustGraduate = 2;
    const personGraduateGraduate = 3;
    const personGraduateGraduateClass = 4;

    // Priority levels
    const personPriorityZero = -1;
    const personPriorityOne = 1;
    const personPriorityTwo = 2;
    const personPriorityThree = 3;
    const personPriorityFour = 4;
    const personPriorityFive = 5;

    // Engineering status
    const personIsEngineeringUnknown = -1;
    const personIsEngineeringYes = 1;
    const personIsEngineeringNo = 2;

    // Character types
    const personCharTypeAll = "all";
    const personCharTypeGraduateBefore = "graduate_before";
    const personCharTypeGraduateJustGraduate = "just_graduate";
    const personCharTypeGraduateGraduate = "graduate";

    // Intention status
    const personIntentionYes = 1;
    const personIntentionNo = 2;
    const personIntentionUnknown = -1;

    // Sign status
    const personSignStatusSign = 1;
    const personSignStatusSignNotFind = 2;
    const personSignStatusSignAppend = 3;

    // Promote compare results
    const personPromoteCompareResultIntentionSign = 1;
    const personPromoteCompareResultIntentionNoSign = 2;
    const personPromoteCompareResultNoIntentionSign = 3;
    const personPromoteCompareResultNoIntentionNoSign = 4;
    const personPromoteCompareResultSign = 5; // 未联系已报名

    const personSign = 1;
    const personNoSign = -1;


    // 体检结果
    const personPhysicalCheckUnknown = -1;
    const personPhysicalCheckYes = 1;
    const personPhysicalCheckNo = 2;

    const personPhysicalRecheckUnknown = -1;
    const personPhysicalRecheckYes = 1;
    const personPhysicalRecheckNo = 2;

    const personPhysicalSpotCheckUnknown = -1;
    const personPhysicalSpotCheckYes = 1;
    const personPhysicalSpotCheckNo = 2;

    const personPhysicalResultYes = 1;
    const personPhysicalResultNo = 2;
    const personPhysicalResultUnknown = -1;

    // 政考结果
    const personPoliticalExamResultPass = 1;
    const personPoliticalExamResultFail = 2;
    const personPoliticalExamResultUnknown = -1;

    // 役前教育结果
    const personEducationResultPass = 1;
    const personEducationResultFail = 2;
    const personEducationResultUnknown = -1;

    const personSexMale = 1;
    const personSexFemale = 2;


    public static function getPersonIsBeijingText($isBeijing)
    {
        switch ($isBeijing) {
            case self::personIsBeijingUnknown:
                return "";
            case self::personIsBeijingYes:
                return "京籍";
            case self::personIsBeijingNo:
                return "非京籍";
            default:
                return "";
        }
    }

    public static function getPersonPreTypeText($preType)
    {
        switch ($preType) {
            case self::personPreTypeUnknown:
                return "";
            case self::personPreTypeDoublePassed:
                return "双合格";
            case self::personPreTypeEducation:
                return "教育";
            default:
                return "";
        }
    }

    public static function getPersonStudyFormText($studyForm)
    {
        switch ($studyForm) {
            case self::personStudyFormUnknown:
                return "未知";
            case self::personStudyFormFullTime:
                return "全日制";
            case self::personStudyFormNonFullTime:
                return "非全日制";
            default:
                return "";
        }
    }

    public static function getPersonEducationText($education)
    {
        switch ($education) {
            case self::personEducationZhuan:
                return "专科";
            case self::personEducationBen:
                return "本科";
            case self::personEducationBenPlus:
                return "本科以上";
            default:
                return "";
        }
    }

    public static function getPersonGraduateText($graduate)
    {
        switch ($graduate) {
            case self::personGraduateBefore:
                return "在校生";
            case self::personGraduateJustGraduate:
                return "应届毕业生";
            case self::personGraduateGraduate:
                return "毕业生";
            case self::personGraduateGraduateClass:
                return "毕业生班生";
            default:
                return "";
        }
    }

    public static function getPersonPriorityText($priority)
    {
        switch ($priority) {
            case self::personPriorityZero:
                return "";
            case self::personPriorityOne:
                return "1类";
            case self::personPriorityTwo:
                return "2类";
            case self::personPriorityThree:
                return "3类";
            case self::personPriorityFour:
                return "4类";
            case self::personPriorityFive:
                return "5类";
            default:
                return "";
        }
    }

    public static function getPersonIsEngineeringText($isEngineering)
    {
        switch ($isEngineering) {
            case self::personIsEngineeringUnknown:
                return "";
            case self::personIsEngineeringYes:
                return "是";
            case self::personIsEngineeringNo:
                return "否";
            default:
                return "";
        }
    }

    public static function getPersonIntentionText($intention)
    {
        switch ($intention) {
            case self::personIntentionYes:
                return "有意向";
            case self::personIntentionNo:
                return "无意向";
            case self::personIntentionUnknown:
                return "未联系";
            default:
                return "";
        }
    }

    public static function getPersonSexText($sex)
    {
        switch ($sex) {
            case self::personSexMale:
                return "男";
            case self::personSexFemale:
                return "女";
            default:
                return "";
        }
    }

    public static function getPersonPhysicalCheckText($physicalCheck)
    {
        switch ($physicalCheck) {
            case self::personPhysicalCheckYes:
                return "合格";
            case self::personPhysicalCheckNo:
                return "不合格";
            case self::personPhysicalCheckUnknown:
                return "未体检";
            default:
                return "";
        }
    }

    public static function getPersonPhysicalRecheckText($physicalRecheck)
    {
        switch ($physicalRecheck) {
            case self::personPhysicalRecheckYes:
                return "合格";
            case self::personPhysicalRecheckNo:
                return "不合格";
            case self::personPhysicalRecheckUnknown:
                return "未复查";
            default:
                return "";
        }
    }

    public static function getPersonPhysicalSpotCheckText($physicalSpotCheck)
    {
        switch ($physicalSpotCheck) {
            case self::personPhysicalSpotCheckYes:
                return "正常";
            case self::personPhysicalSpotCheckNo:
                return "异常";
            case self::personPhysicalSpotCheckUnknown:
                return "未抽查";
            default:
                return "";
        }
    }

    public static function getPersonPoliticalExamResultText($politicalExamResult)
    {
        switch ($politicalExamResult) {
            case self::personPoliticalExamResultPass:
                return "通过";
            case self::personPoliticalExamResultFail:
                return "不通过";
            case self::personPoliticalExamResultUnknown:
                return "未政考";
            default:
                return "";
        }
    }

    public static function getPersonEducationResultText($educationResult)
    {
        switch ($educationResult) {
            case self::personEducationResultPass:
                return "通过";
            case self::personEducationResultFail:
                return "不通过";
            case self::personEducationResultUnknown:
                return "未教育";
            default:
                return "";
        }
    }

    public static function getPersonPhysicalResultText($physicalResult)
    {
        switch ($physicalResult) {
            case self::personPhysicalResultYes:
                return "合格";
            case self::personPhysicalResultNo:
                return "不合格";
            case self::personPhysicalResultUnknown:
                return "未知";
            default:
                return "";
        }
    }

    public static function getPersonSignText($sign)
    {
        switch ($sign) {
            case self::personSignStatusSign:
                return "已报名";
            case self::personSignStatusSignNotFind:
                return "未找到";
            case self::personSignStatusSignAppend:
                return "补充报名";
            default:
                return "未报名";
        }
    }

    public static function getPersonPromoteCompareResultText($promoteCompareResult)
    {
        switch ($promoteCompareResult) {
            case self::personPromoteCompareResultIntentionSign:
                return "有意向已报名";
            case self::personPromoteCompareResultIntentionNoSign:
                return "有意向未报名";
            case self::personPromoteCompareResultNoIntentionSign:
                return "无意向已报名";
            case self::personPromoteCompareResultNoIntentionNoSign:
                return "无意向未报名";
            case self::personPromoteCompareResultSign:
                return "未联系已报名";
            default:
                return "未联系";
        }
    }
}
