<?php

namespace App\Http\Controllers\Admin;

use App\Http\Services\Common\ConfigService;

class ConfigController
{

    public function get()
    {
        $service  = new ConfigService();
        $response = $service->get();
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function update()
    {
        $params   = request()->all();
        $service  = new ConfigService();
        $response = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }
}
