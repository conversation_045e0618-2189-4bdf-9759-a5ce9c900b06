<?php

namespace App\Http\Services\Admin;

use App\Exceptions\ErrorTrait;
use App\Http\Consts\RegionConst;
use App\Http\Utils\ScopeQueryImpl;
use App\Models\InvitationCode;
use App\Models\Region;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class InvitationCodeService
{
    use ErrorTrait;


    public function index($params)
    {
        $user    = auth()->user();
        $builder = ScopeQueryImpl::adminGetInvitationCodeQuery($user, $params);

        $items = $builder->paginate(pageSize());
        return $items;
    }

    public function destroy($id, $params)
    {
        if ($id == "batch") {
            $ids = $params['ids'];
            if (is_array($ids)) {
                InvitationCode::query()->whereIn('id', $ids)->delete();
            }
        } else {
            InvitationCode::query()->where('id', $id)->delete();
        }
        return true;
    }


    public function removeAll($params)
    {
        $builder = ScopeQueryImpl::adminGetInvitationCodeQuery(auth()->user(), $params);
        $builder->delete();
        return true;
    }

    public function generate()
    {
        $departments = Region::query()->whereIn('level', [
            RegionConst::levelStreet,
            RegionConst::levelDepartment,
            RegionConst::levelSchool,
            RegionConst::levelHospital,
            RegionConst::levelPolice
        ])
            ->whereDoesntHave('invitationCode')
            ->get();
        foreach ($departments as $department) {
            $code = Str::lower(Str::random(6));
            while (InvitationCode::query()->where('code', $code)->exists()) {
                $code = Str::lower(Str::random(6));
            }
            $invitationCode                       = new InvitationCode();
            $invitationCode->code                 = $code;
            $invitationCode->department_tree_code = $department->tree_code;
            $invitationCode->department_code      = $department->code;
            $invitationCode->department_name      = $department->name;
            $invitationCode->expired_at           = now()->addYear();
            $invitationCode->save();
        }
    }

    public function update($id, array $params)
    {
        $v = $params['v'] ?? '';
        if ($v == 'switch') {
            unset($params['v']);
            $item = InvitationCode::query()->find($id);
            $item->update($params);
            return $item;
        }
    }
}
