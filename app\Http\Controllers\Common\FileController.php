<?php

namespace App\Http\Controllers\Common;

use App\Http\Services\Common\FileService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class FileController
{
    private $service;

    public function __construct()
    {
        $this->service = new FileService();
    }

    /**
     * 上传文件
     */
    public function uploadFile(Request $request)
    {
        // 检测是否登录
        $userId = 0;

//        if (auth('user')->check()) {
//            $user = auth('user')->user();
//            $userId = $user->id;
//        }

        $file     = $request->file('file');
        $compress = $request->input('compress', false);
        $folder   = $request->input('folder', 'default');
        $folderId = $request->input('folder_id', 0);
        $response = $this->service->uploadFile($file, $folderId, $folder, $compress, $userId);
        if ($this->service->hasError()) {
            return error($this->service->getError());
        }
        return success($response);
    }

    /**
     * 获取文件
     */
    public function getFile(Request $request)
    {
        $files_id = $request->input('files_id');
        if (Str::contains($files_id, ',')) {
            $files_id = explode(',', $files_id);
        }
        $response = $this->service->getFile($files_id);
        if ($this->service->hasError()) {
            return error($this->service->getError());
        }
        return success($response);
    }

    /**
     * 默认头像
     */
    public function defaultAvatar()
    {
        $params   = Request::capture()->input();
        $str      = $params['str'] ?? 'A';
        $response = $this->service->defaultAvatar($str);
        if ($this->service->hasError()) {
            return "";
        }
        return $response;
    }

    /**
     * 添加文件
     */
    public function addFile()
    {
        $params = Request::capture()->input();
        $response = $this->service->addFile($params);
        if ($this->service->hasError()) {
            return error($this->service->getError());
        }
        return success($response);
    }

    public function delete()
    {
        $params = Request::capture()->input();
        $response = $this->service->delete($params);
        if ($this->service->hasError()) {
            return error($this->service->getError());
        }
        return success($response);
    }
}
