<?php

namespace App\Http\Controllers\Admin\Task;

use App\Http\Services\Admin\Task\TaskService;

class TaskController
{
    public function index()
    {
        $params  = request()->all();
        $service = new TaskService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store()
    {
        $params  = request()->all();
        $service = new TaskService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }


    public function confirm()
    {
        $params  = request()->all();
        $service = new TaskService();
        $resp    = $service->confirm($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
