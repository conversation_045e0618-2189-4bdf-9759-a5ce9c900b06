<?php

namespace App\Http\Controllers\Admin\Recall;

use App\Http\Services\Admin\Recall\RecallTaskSoldierService;
use Illuminate\Http\Request;

class RecallTaskSoldierController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new RecallTaskSoldierService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new RecallTaskSoldierService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new RecallTaskSoldierService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new RecallTaskSoldierService();
        $resp    = $service->update($params, $id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $service  = new RecallTaskSoldierService();
        $params   = $request->all();
        $response = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function clear()
    {
        $params   = request()->input();
        $service  = new RecallTaskSoldierService();
        $response = $service->clear($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($response);
    }

    public function searchOption(Request $request)
    {
        $params  = $request->all();
        $service = new RecallTaskSoldierService();
        $resp    = $service->searchOption($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
