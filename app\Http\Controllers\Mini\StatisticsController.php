<?php

namespace App\Http\Controllers\Mini;

use App\Http\Services\Mini\StatisticsService;

class StatisticsController
{
    public function courseStatistics()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->courseStatistics($params);
        if ($service->hasError()) {
            return error($service->hasError());
        }
        return success($resp);
    }

    public function paperStatistics()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->paperStatistics($params);
        if ($service->hasError()) {
            return error($service->hasError());
        }
        return success($resp);
    }

    public function paperResultStatistics()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->paperResultStatistics($params);
        if ($service->hasError()) {
            return error($service->hasError());
        }
        return success($resp);
    }

    public function workTaskStatistics()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->workTaskStatistics($params);
        if ($service->hasError()) {
            return error($service->hasError());
        }
        return success($resp);
    }


    public function recallTaskStatistics()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->recallTaskStatistics($params);
        if ($service->hasError()) {
            return error($service->hasError());
        }
        return success($resp);
    }

    public function outNoticeTaskStatistics()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->outNoticeTaskStatistics($params);
        if ($service->hasError()) {
            return error($service->hasError());
        }
        return success($resp);
    }

    public function remindCount()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->remindCount($params);
        if ($service->hasError()) {
            return error($service->hasError());
        }
        return success($resp);
    }

    /**
     * 学校人员统计列表
     */
    public function missionSchoolPersonList()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->missionSchoolPersonList($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    /**
     * 社会人员统计列表
     */
    public function missionSocialPersonList()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->missionSocialPersonList($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    /**
     * 意向统计列表
     */
    public function missionIntentionList()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->missionIntentionList($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    /**
     * 宣传比对统计列表
     */
    public function missionPromoteCompareList()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->missionPromoteCompareList($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    /**
     * 政考统计列表
     */
    public function missionPoliticalExamList()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->missionPoliticalExamList($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    /**
     * 教育统计列表
     */
    public function missionEducationList()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->missionEducationList($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    /**
     * 完成数统计列表
     */
    public function missionGoList()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->missionGoList($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    /**
     * 体检统计列表
     */
    public function missionPhysicalCheckList()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->missionPhysicalCheckList($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    /**
     * 体检复查统计列表
     */
    public function missionPhysicalRecheckList()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->missionPhysicalRecheckList($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    /**
     * 体检抽查统计列表
     */
    public function missionPhysicalSpotCheckList()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->missionPhysicalSpotCheckList($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }

    /**
     * 任务数统计列表
     */
    public function missionTaskNumList()
    {
        $params  = request()->all();
        $service = new StatisticsService();
        $resp    = $service->missionTaskNumList($params);
        if ($service->hasError()) {
            return error($service->getError(), $service->getCode());
        }
        return success($resp);
    }
}
