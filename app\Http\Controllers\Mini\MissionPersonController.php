<?php

namespace App\Http\Controllers\Mini;

use App\Http\Services\Mini\MissionPersonService;
use Illuminate\Http\Request;

class MissionPersonController
{
    /**
     * 获取任务人员列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $params = $request->all();
        $service = new MissionPersonService();
        $resp = $service->index($params);
        
        if ($service->hasError()) {
            return error($service->getError());
        }
        
        return success($resp);
    }

    /**
     * 获取任务人员详情
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        $params = $request->all();
        $service = new MissionPersonService();
        $resp = $service->show($id, $params);
        
        if ($service->hasError()) {
            return error($service->getError());
        }
        
        return success($resp);
    }

    /**
     * 更新任务人员信息
     * 
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $params = $request->all();
        $service = new MissionPersonService();
        $resp = $service->update($id, $params);
        
        if ($service->hasError()) {
            return error($service->getError());
        }
        
        return success($resp);
    }
} 