<?php

namespace App\Http\Controllers\Admin\OutNotice;

use App\Http\Services\Admin\OutNotice\CcsRobotTemplateService;
use Illuminate\Http\Request;

class CcsRobotTemplateController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new CcsRobotTemplateService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function store(Request $request)
    {
        $params  = $request->all();
        $service = new CcsRobotTemplateService();
        $resp    = $service->store($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show(Request $request, $id)
    {
        $params  = $request->all();
        $service = new CcsRobotTemplateService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new CcsRobotTemplateService();
        $resp    = $service->update($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function destroy(Request $request, $id)
    {
        $params  = $request->all();
        $service = new CcsRobotTemplateService();
        $resp    = $service->destroy($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }


}
