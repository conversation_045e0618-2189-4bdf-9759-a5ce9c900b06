<?php

namespace App\Http\Controllers\Mini;

use App\Http\Services\Mini\PaperService;

class PaperController
{
    public function detail($id)
    {
        $params  = request()->all();
        $service = new PaperService();
        $resp    = $service->detail($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function start($id)
    {
        $params  = request()->all();
        $service = new PaperService();
        $resp    = $service->start($id, $params, '');
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function nextQuestion($roundId)
    {
        $params = request()->all();

        $service = new PaperService();
        $resp    = $service->nextQuestion($roundId, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function preQuestion($roundId)
    {
        $params = request()->all();

        $service = new PaperService();
        $resp    = $service->preQuestion($roundId, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function answer($roundId)
    {
        $params  = request()->all();
        $service = new PaperService();
        $resp    = $service->answer($roundId, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function finish($roundId)
    {
        $params  = request()->all();
        $service = new PaperService();
        $resp    = $service->finish($roundId, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function examPaper()
    {
        $params  = request()->all();
        $service = new PaperService();
        $resp    = $service->examPaper($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    /**
     * 做完题后查看
     */
    public function questions($roundId)
    {
        $params  = request()->all();
        $service = new PaperService();
        $resp    = $service->questions($roundId);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
