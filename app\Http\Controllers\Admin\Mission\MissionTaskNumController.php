<?php

namespace App\Http\Controllers\Admin\Mission;

use App\Http\Services\Admin\Mission\MissionTaskNumService;
use Illuminate\Http\Request;

class MissionTaskNumController
{
    public function index(Request $request)
    {
        $params  = $request->all();
        $service = new MissionTaskNumService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function update(Request $request, $id)
    {
        $params  = $request->all();
        $service = new MissionTaskNumService();
        $resp    = $service->update($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show($id)
    {
        $service = new MissionTaskNumService();
        $resp    = $service->show($id);
        return success($resp);
    }
}
