<?php

namespace App\Http\Controllers\Admin\Task;

use App\Http\Services\Admin\Task\ImportItemService;

class ImportItemController
{
    public function index()
    {
        $params  = request()->all();
        $service = new ImportItemService();
        $resp    = $service->index($params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }

    public function show($id)
    {
        $service = new ImportItemService();
        $resp    = $service->show($id);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }


    public function update($id)
    {
        $params  = request()->all();
        $service = new ImportItemService();
        $resp    = $service->update($id, $params);
        if ($service->hasError()) {
            return error($service->getError());
        }
        return success($resp);
    }
}
