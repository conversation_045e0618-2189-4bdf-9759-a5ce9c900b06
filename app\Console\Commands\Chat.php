<?php

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Workerman\Worker;

class Chat extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:chat {action} {--daemonize}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        global $argv;//定义全局变量
        $arg     = $this->argument('action');
        $argv[1] = $arg;
        $argv[2] = $this->option('daemonize') ? '-d' : '';//该参数是以daemon（守护进程）方式启动

        global $text_worker;
        // 创建一个Worker监听2345端口，使用websocket协议通讯
        $text_worker                 = new Worker("websocket://0.0.0.0:8008");
        $text_worker->uidConnections = array();//在线用户连接对象
        $text_worker->uidInfo        = array();//在线用户的用户信息
        // 启动4个进程对外提供服务
        $text_worker->count = 4;
        //当启动workerman的时候 触发此方法
        $text_worker->onWorkerStart = function () {

        };
        //当浏览器连接的时候触发此函数
        $text_worker->onConnect = function ($connection) {

        };
        //向用户发送信息的时候触发
        //$connection 当前连接的人的信息 $data 发送的数据
        $text_worker->onMessage = function ($connection, $data) {
            $data   = json_decode($data, true);
            $prompt = $data['prompt'] ?? '';
            if ($prompt) {
                $sessionId = $data['session_id'] ?? '';
                $this->blianChat($prompt, $sessionId, $connection);
            }
        };
        //浏览器断开链接的时候触发
        $text_worker->onClose = function ($connection) {

        };
        Worker::runAll();
    }


    public function blianChat($prompt, $sessionId, $connection)
    {
        $apiKey      = env("BAILIAN_API_KEY");
        $appId       = env('BAILIAN_CONSULT_CHAT_APP_ID');
        $workspaceId = env('BAILIAN_WORKSPACE_ID');
        $url         = "https://dashscope.aliyuncs.com/api/v1/apps/{$appId}/completion";
        $headers     = [
            'Content-Type: application/json',
            "Authorization: {$apiKey}",
            'X-DashScope-SSE: enable',
            "X-DashScope-WorkSpace: {$workspaceId}",
        ];
        $data        = [
            'input' => ['prompt' => $prompt],
//            'parameters' => [
//                'has_thoughts' => true,
//            ],
        ];
        if ($sessionId) {
            $data['input']['session_id'] = $sessionId;
        }


        // Initialize cURL session
        $ch = curl_init($url);

        // Set cURL options
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

        // To handle the SSE, we need to set up a write function
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function ($ch, $data) use ($connection) {
//            echo $data;
            // Flush the output buffer to handle real-time streaming
//            ob_flush();
//            flush();
//            收到服务器内容：id:8
//event:result
//            :HTTP_STATUS/200
//data:{"output":{"session_id":"db90318b32ed48edbd2d7c15282c9861","finish_reason":"null","text":"嗨！很高兴见到你！你的笑声真有感染力呀！哈哈哈哈哈！有什么有趣的事情发生了吗？或者需要我分享一些搞笑的东西来延续这份快乐吗？"},"usage":{"models":[{"input_tokens":36,"output_tokens":36,"model_id":"qwen-max"}]},"request_id":"255e8cac-2d31-94da-9488-bac6a8f5630a"}

            // 使用正则表达式提取JSON部分
            preg_match('/data:(\{.*\})/', $data, $matches);

            if (isset($matches[1])) {
                $jsonString = $matches[1];

                if (json_last_error() === JSON_ERROR_NONE) {
                    // 成功解析JSON数据
                    $connection->send($jsonString);
                } else {
                    // JSON解析错误
                    echo "JSON解析错误：" . json_last_error_msg();
                }
            } else {
                echo "未能找到JSON数据";
            }
            return strlen($data);
        });


        // Execute cURL session
        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            throw new Exception(curl_error($ch));
        }

        // Close cURL session
        curl_close($ch);
    }
}
