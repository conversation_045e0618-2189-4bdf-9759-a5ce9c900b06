<template>
  <div class="list-common-table">
    <person-search-form
      :name="true"
      :phone="true"
      :department-code="true"
      :id-card="true"
      :age="true"
      :admission-year="true"
      :graduation-year="true"
      :education-system="true"
      :is-beijing="true"
      :education="true"
      :graduate="true"
      :degree="true"
      :study-form="true"
      :major="true"
      :nation="true"
      :political-outlook="true"
      :priority="true"
      :pre-type="true"
      :intention="false"
      :promote-compare-result="true"
      :promote-compare-result-advance="false"
      :form-data="formData"
      @search="onSearch"
      @reset="onReset"
    />
    <t-space style="margin-top: 16px">
      <t-popconfirm
        v-if="selectedRowKeys.length > 0"
        content="确认删除吗"
        @confirm="handleClickDelete(selectedRowKeys)"
      >
        <t-button theme="danger">
          <template #icon>
            <t-icon name="delete" />
          </template>
          批量删除
        </t-button>
      </t-popconfirm>
      <export-button
        :form-data="{ ...formData, ids: selectedRowKeys }"
        :task-type="taskTypeMap.export_mission_physical_spot_check_person_list.status"
        :btn-title="'导出人员名单'"
      />

      <import-button
        :task-type="taskTypeMap.import_mission_physical_spot_check_result.status"
        :btn-text="'导入结果 (区)'"
        template-url="体检抽查结果人员名单.xlsx"
      />

      <physical-examination-spot-check-button :mission-id="missionId" />
    </t-space>

    <div class="table-container">
      <t-table
        :select-on-row-click="true"
        :data="list"
        :columns="COLUMNS"
        :row-key="rowKey"
        :vertical-align="verticalAlign"
        :hover="hover"
        :pagination="pagination"
        :loading="dataLoading"
        :header-affixed-top="headerAffixedTop"
        :selected-row-keys="selectedRowKeys"
        @page-change="rehandlePageChange"
        @change="rehandleChange"
        @select-change="rehandleSelectChange"
      >
        <template #op="slotProps">
          <t-space @click.stop>
            <t-link theme="primary" @click="handleClickEdit(slotProps)"> 修改</t-link>
            <t-link theme="primary" @click="handleClickDetail(slotProps)"> 详情</t-link>
            <t-popconfirm content="确认删除吗" @confirm="handleClickDelete([slotProps.row.id])">
              <t-link theme="danger"> 删除</t-link>
            </t-popconfirm>
            <!--设置检查结果-->
            <t-link theme="primary" @click="handleClickSetResult(slotProps)"> 设置检查结果（区）</t-link>
          </t-space>
        </template>

        <!--性别-->
        <template #sex="slotProps">
          <t-tag :color="personSexMap[slotProps.row.sex]?.color" variant="light">
            {{ personSexMap[slotProps.row.sex]?.text ?? '未知' }}
          </t-tag>
        </template>
        <!--文化程度-->
        <template #education="slotProps">
          <t-tag :color="personEducationMap[slotProps.row.education]?.color" variant="light">
            {{ personEducationMap[slotProps.row.education]?.text ?? '未知' }}
          </t-tag>
        </template>

        <!--学业情况-->
        <template #graduate="slotProps">
          <t-tag :color="personGraduateMap[slotProps.row.graduate]?.color" variant="light">
            {{ personGraduateMap[slotProps.row.graduate]?.text ?? '未知' }}
          </t-tag>
        </template>

        <!--户籍-->
        <template #is_beijing="slotProps">
          <t-tag :color="personIsBeijingMap[slotProps.row.is_beijing]?.color" variant="light">
            {{ personIsBeijingMap[slotProps.row.is_beijing]?.text ?? '未知' }}
          </t-tag>
        </template>
        <!--优先条件-->
        <template #priority="slotProps">
          <t-tag
            v-if="slotProps.row.priority > 0"
            :color="personPriorityMap[slotProps.row.priority]?.color"
            variant="light"
          >
            {{ personPriorityMap[slotProps.row.priority]?.text ?? '无' }}
          </t-tag>
        </template>
        <!--预储人员-->
        <template #pre_type="slotProps">
          <t-tag
            v-if="slotProps.row.pre_type > 0"
            :color="personPreTypeMap[slotProps.row.pre_type]?.color"
            variant="light"
          >
            {{ personPreTypeMap[slotProps.row.pre_type]?.text ?? '无' }}
          </t-tag>
        </template>
        <!--街道-->
        <template #street="slotProps"> {{ slotProps.row.district_name }} / {{ slotProps.row.street_name }}</template>
        <template #physical_check="slotProps">
          <t-tag :color="missionPhysicalCheckMap[slotProps.row.physical_check]?.color" variant="light">
            {{ missionPhysicalCheckMap[slotProps.row.physical_check]?.text ?? '无' }}
          </t-tag>
        </template>

        <template #physical_recheck="slotProps">
          <t-tag :color="missionPhysicalRecheckMap[slotProps.row.physical_recheck]?.color" variant="light">
            {{ missionPhysicalRecheckMap[slotProps.row.physical_recheck]?.text ?? '无' }}
          </t-tag>
        </template>

        <template #physical_result="slotProps">
          <t-tag :color="missionPhysicalResultMap[slotProps.row.physical_result]?.color" variant="light">
            {{ missionPhysicalResultMap[slotProps.row.physical_result]?.text ?? '无' }}
          </t-tag>
        </template>

        <template #physical_spot_check="slotProps">
          <t-tag :color="missionPhysicalSpotCheckMap[slotProps.row.physical_spot_check]?.color" variant="light">
            {{ missionPhysicalSpotCheckMap[slotProps.row.physical_spot_check]?.text ?? '无' }}
          </t-tag>
        </template>
      </t-table>
    </div>
    <person-create-dialog
      :visible="createDialogVisible"
      :row="operateRow"
      @close="createDialogVisible = false"
      @success="handleCreateSuccess"
    />
    <person-detail-dialog :visible="detailDialogVisible" :row="operateRow" @close="detailDialogVisible = false" />
    <set-result-dialog
      :visible="setResultDialogVisible"
      :row="operateRow"
      @close="setResultDialogVisible = false"
      @success="handleClickSetResultSuccess"
    />
  </div>
</template>
<script setup lang="tsx">
import { PageInfo, PrimaryTableCol, TableProps, TableRowData } from 'tdesign-vue-next';
import { computed, onMounted, ref } from 'vue';

import { getMissionPersonList } from '@/api/mission/missionPerson';
import { deleteSocialPerson } from '@/api/person/socialPerson';
import PersonDetailDialog from '@/components/person/personDetail/personDetailDialog.vue';
import PersonSearchForm from '@/components/person/personSearchForm.vue';
import PersonCreateDialog from '@/components/person/personUpdate/personCreateDialog.vue';
import ExportButton from '@/components/task/excel/exportButton.vue';
import ImportButton from '@/components/task/excel/importButton.vue';
import PhysicalExaminationSpotCheckButton from '@/components/task/normal/physicalExaminationSpotCheck/physicalExaminationSpotCheckButton.vue';
import { prefix } from '@/config/global';
import {
  missionPhysicalCheckMap,
  missionPhysicalRecheckMap,
  missionPhysicalResultMap,
  missionPhysicalSpotCheckMap,
  personEducationMap,
  personGraduateMap,
  personIsBeijingMap,
  personListSceneMap,
  personPreTypeMap,
  personPriorityMap,
  personSexMap,
  taskTypeMap,
} from '@/constants';
import SetResultDialog from '@/pages/mission/process/physicalExamination/components/spotCheck/setResultDialog.vue';
import { useSettingStore } from '@/store';

const store = useSettingStore();

const props = defineProps({
  missionId: {
    type: Number,
    default: 0,
  },
});

const COLUMNS: PrimaryTableCol[] = [
  {
    colKey: 'row-select',
    fixed: 'left',
    type: 'multiple',
    align: 'left',
  },
  {
    title: '名称',
    colKey: 'name',
    fixed: 'left',
    width: 120,
    ellipsis: true,
  },
  //  性别
  {
    title: '性别',
    colKey: 'sex',
    width: 80,
  },
  // 年龄
  {
    title: '年龄',
    colKey: 'age',
    width: 80,
  },
  // education
  {
    title: '文化程度',
    colKey: 'education',
    width: 100,
  },
  // 取得学位
  {
    title: '取得学位',
    colKey: 'degree',
    width: 100,
  },
  // 学业情况
  {
    title: '学业情况',
    colKey: 'graduate',
    width: 100,
  },
  // 户籍
  {
    title: '户籍',
    colKey: 'is_beijing',
    width: 100,
  },
  // 优先条件
  {
    title: '优先条件',
    colKey: 'priority',
    width: 100,
  },
  // 预储人员
  {
    title: '预储人员',
    colKey: 'pre_type',
    width: 100,
  },
  // 户籍地
  {
    title: '户籍地',
    colKey: 'address',
    width: 150,
    ellipsis: true,
  },
  // 初次体检结果
  {
    title: '初次体检结果',
    colKey: 'physical_check',
    width: 100,
  },
  // 复查结果
  {
    title: '复查结果',
    colKey: 'physical_recheck',
    width: 100,
  },
  // 抽查结果
  {
    title: '抽查结果',
    colKey: 'physical_spot_check',
    width: 100,
  },
  // 最终结果
  {
    title: '最终结果',
    colKey: 'physical_result',
    width: 100,
  },
  {
    align: 'left',
    fixed: 'right',
    width: 300,
    colKey: 'op',
    title: '操作',
  },
];

const searchFormInit = {
  name: '',
  phone: '',
  department_code: '',
  id_card: '',
  age: '',
  sex: '',
  admission_year: '',
  graduation_year: '',
  education_system: '',
  is_beijing: '',
  education: '',
  graduate: '',
  degree: '',
  study_form: '',
  major: '',
  nation: '',
  political_outlook: '',
  priority: '',
  pre_type: '',
  person_type: '',
  occupation_certificate: '',
  mission_id: props.missionId,
};

const formData = ref({ ...searchFormInit });
const rowKey = 'id';
const verticalAlign = 'top' as const;
const hover = true;

const pagination = ref({
  defaultPageSize: 10,
  total: 100,
  defaultCurrent: 1,
});

const list = ref([]);

const dataLoading = ref(false);

onMounted(() => {
  fetchData();
});

const fetchData = async () => {
  dataLoading.value = true;
  try {
    const query = {
      ...formData.value,
      page: pagination.value.defaultCurrent,
      page_size: pagination.value.defaultPageSize,
      scene: personListSceneMap.mission_physical_spot_check_person_list.status,
    };
    const { data, total, current_page, per_page } = await getMissionPersonList(query);
    list.value = data;
    pagination.value = {
      total,
      defaultCurrent: current_page,
      defaultPageSize: parseInt(String(per_page), 10),
    };
  } catch (e) {
    console.log(e);
  } finally {
    dataLoading.value = false;
  }
};

const createDialogVisible = ref(false);

const operateRow = ref({});
const handleClickEdit = async ({ row }: any) => {
  operateRow.value = row;
  createDialogVisible.value = true;
};

const detailDialogVisible = ref(false);
const handleClickDetail = async ({ row }: any) => {
  operateRow.value = row;
  detailDialogVisible.value = true;
};

const handleCreateSuccess = () => {
  fetchData();
  createDialogVisible.value = false;
};

// 多选
const selectedRowKeys = ref<TableProps['selectedRowKeys']>([]);
const rehandleSelectChange: TableProps['onSelectChange'] = (value, ctx) => {
  selectedRowKeys.value = value;
  console.log(value, ctx);
};

// 删除

const handleClickDelete = (ids: Array<String | Number>) => {
  deleteSocialPerson({
    id: 'batch',
    ids,
  }).then((res) => {
    console.log(res);
    fetchData();
  });
};

// 搜索
const onReset = (val: unknown) => {
  console.log(val);
  formData.value = { ...searchFormInit };
};
const onSearch = (val: unknown) => {
  console.log(val);
  console.log(formData.value);
  fetchData();
};
const rehandlePageChange = (pageInfo: PageInfo, newDataSource: TableRowData[]) => {
  console.log('分页变化', pageInfo, newDataSource);
  pagination.value.defaultPageSize = pageInfo.pageSize;
  pagination.value.defaultCurrent = pageInfo.current;
  fetchData();
};
const rehandleChange = (changeParams: unknown, triggerAndData: unknown) => {
  console.log('统一Change', changeParams, triggerAndData);
};

const headerAffixedTop = computed(
  () =>
    ({
      offsetTop: store.isUseTabsRouter ? 48 : 0,
      container: `.${prefix}-layout`,
    }) as any, // TO BE FIXED
);

const setResultDialogVisible = ref(false);
const handleClickSetResult = ({ row }: any) => {
  console.log('handleClickSetResult');
  setResultDialogVisible.value = true;
  operateRow.value = row;
};
const handleClickSetResultSuccess = () => {
  fetchData();
  console.log('handleClickSetResultSuccess');
  setResultDialogVisible.value = false;
};
</script>

<style lang="less" scoped>
.list-common-table {
  background-color: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);

  .table-container {
    margin-top: var(--td-comp-margin-xxl);
  }
}

.form-item-content {
  width: 100%;
}

.operation-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 0 !important;

  .expand {
    .t-button__text {
      display: flex;
      align-items: center;
    }
  }
}

.payment-col {
  display: flex;

  .trend-container {
    display: flex;
    align-items: center;
    margin-left: var(--td-comp-margin-s);
  }
}
</style>
