<?php

namespace App\Console\Commands;

use App\Http\Utils\WechatApp;
use Illuminate\Console\Command;

class RefreshAccessToken extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:refresh-access-token';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $app = WechatApp::getMiniApp();
        $app->getAccessToken()->refresh();
        $this->info('refresh access token success');
    }
}
